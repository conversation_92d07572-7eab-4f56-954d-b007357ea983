#ifndef _UTILEX_THREAD_HPP_
#define _UTILEX_THREAD_HPP_

#include <utilex/crt.hpp>

#ifdef __cplusplus

	class ithread 
	{
	public:
		ithread() {
			thread = INVALID_HANDLE_VALUE;
			_sem_init(&this->sem, 0);
		}
		virtual ~ithread() {
			_sem_destroy(&this->sem);
		}
		int Start_Thread() {

			rc_assert(thread == INVALID_HANDLE_VALUE, S_ERROR)

			this->Thread_Init();
			int rc = _thread_create(&thread, ithread::thread_proc, this);
			rc_assert(rc == S_SUCCESS,S_ERROR)
			this->WaitSem();
			return S_SUCCESS;
		}

		int Stop_Thread() {
			rc_assert(thread != INVALID_HANDLE_VALUE, S_ERROR)
			this->Thread_UnInit();
			this->WaitSem();
			return S_SUCCESS;
		}

		int PostSem() {
			rc_assert(thread != INVALID_HANDLE_VALUE, S_ERROR)
			_sem_post(&this->sem);
			return S_SUCCESS;
		}
		int WaitSem() {
			rc_assert(thread != INVALID_HANDLE_VALUE, S_ERROR)
			_sem_wait(&this->sem);
			return S_SUCCESS;
		}
		static void thread_proc(void* pthis) {
			ithread* p = static_cast<ithread*>(pthis);
			if (p)
			{				
				p->PostSem();
				p->Thread_Run();
				p->PostSem();
			}
		}
	protected:
		std_method(Thread_Init)() PURE;
		std_method(Thread_Run)() PURE;
		std_method(Thread_UnInit)() PURE;
	protected:
		_thread_t		thread;
		_sem_t			sem;
	};

#endif


#endif
