#pragma once

#include <string>
#include <memory>
#include <map>
#include <mutex>
#include <boost/process/detail/child_decl.hpp>
#include <vector>
#include <thread>
#include <atomic>

#if !defined(tstring)
#if defined(_WIN32)
using tstring = std::wstring;
#else
using tstring = std::string;
#endif
#endif

using std::map;
using std::shared_ptr;

namespace bp = boost::process;

class ProcManager
{
public:

	ProcManager();
	~ProcManager();

	bool StartupProcByPath(const tstring& proc_path);
	bool StopProcByPath(const tstring& proc_path);

	bool IsProcRunning(const tstring& proc_path);
	bool IsProcRunning(const tstring& proc_path, std::vector<bp::pid_t>& pids);

private:
	enum class ProcInfoEnum
	{
		kOnlyProcRun,
		kGetProcRunPid
	};

	enum class ProcStatus
	{
		kStartup,
		kStop
	};

	bool IsProcRunning(const tstring& proc_path, ProcInfoEnum flags, std::vector<bp::pid_t>& pids);

	void ProcWatchDog();

	map<tstring, shared_ptr<bp::child>> run_proc_cache_;
	std::mutex cache_mtx_;

	map<tstring, ProcStatus> proc_status_;
	std::shared_ptr<std::thread> proc_watchdog_thread_;
	std::atomic_bool watchdog_isopen;
};


