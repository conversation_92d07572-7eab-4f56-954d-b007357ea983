#ifndef _X_MSC_DISPATCHER_H_
#define _X_MSC_DISPATCHER_H_

#include "XMessage.h"
#include "msc\IAtBuffer.h"

/////////////////////////////////////////////////////////////////////////////
#ifdef USE_COMMONDLL
class XMSCDispatcher
{
public:
	virtual VOID WINAPI Handle(XMessage* lpXMessage) = 0;
	virtual VOID WINAPI FireConnectBroken() = 0;
};
#endif 

#ifdef USE_RISINGCOM
class XMSCDispatcher
{
public:
	virtual VOID WINAPI Handle(ATMSGID dwMessageID,IAtBuffer* lpXMessage) = 0;
	virtual VOID WINAPI FireConnectBroken() = 0;
};

interface IMSCDispatcher : public IUnknown
{
	virtual VOID WINAPI Handle(ATMSGID dwMessageID,IAtBuffer* lpXMessage) = 0;
	virtual VOID WINAPI FireConnectBroken() = 0;
};
AT_DEFINE_IID(IMSCDispatcher,"{941534FA-C1B6-4bd7-AD6D-9429B8D17711}");
#endif

/////////////////////////////////////////////////////////////////////////////
#endif