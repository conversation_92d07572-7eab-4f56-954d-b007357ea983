#ifndef	_UTIL_CORE_HPP_
#define _UTIL_CORE_HPP_


#include <util/targetos.hpp>

#if 1
#define LIKELY(x)	__builtin_expect(!!(x), 1)
#define UNLIKELY(x)	__builtin_expect(!!(x), 0)
#else
#define	LIKELY
#define	UNLIKELY
#endif

#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

#ifndef __STDC_CONSTANT_MACROS
#define __STDC_CONSTANT_MACROS
#endif

#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS
#endif


#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

#ifndef __USE_UNIX98
#define __USE_UNIX98
#endif


#include <stdio.h>
#include <stdlib.h>
#include <stddef.h>
#include <stdarg.h>
#include <string.h>
#include <wchar.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <stdint.h>
#include <assert.h>
#include <time.h>




#if (TARGET_OS == OS_WINDOWS)
	#include <util/win/osdef.h>
#elif (TARGET_OS == OS_POSIX)
	#include <util/posix/osdef.h>
#elif (TARGET_OS == OS_ANDROID)
	#include <util/posix/osdef.h>
#elif (TARGET_OS == OS_DARWIN)
	#include <util/unix/osdef.h>
#endif



#include <util/common.hpp>
#include <util/bits.hpp>

#if (TARGET_OS == OS_WINDOWS)
	#include <util/win/ossysdef.h>
	#include <util/win/sysvar.hpp>
#elif (TARGET_OS == OS_POSIX)
	#include <util/posix/ossysdef.h>
	#include <util/posix/sysvar.hpp>
#elif (TARGET_OS == OS_DARWIN)
	#include <util/unix/ossysdef.h>
	#include <util/unix/sysvar.hpp>
#endif


#ifndef event_id
typedef unsigned long	event_id;
#endif

#if (TARGET_OS == OS_WINDOWS)
	typedef wchar_t				basic_tchar;
	typedef const wchar_t*		LPSTRING;
#elif (TARGET_OS == OS_POSIX)
	typedef char				basic_tchar;
	typedef const char*			LPSTRING;
#elif (TARGET_OS == OS_DARWIN)
	typedef char				basic_tchar;
	typedef const char*			LPSTRING;
#endif

#define _max(a,b)								((a)>(b)?(a):(b))
#define _min(a,b)								((a)<(b)?(a):(b))



#endif 
