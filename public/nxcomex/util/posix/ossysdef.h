#ifndef _UTIL_OSSYSDEF_H_
#define _UTIL_OSSYSDEF_H_

#include <util/core.hpp>

#ifndef FALSE
#define FALSE 0
#define TRUE 1
#endif // !FALSE


#ifndef NULL
    #ifdef __cplusplus
        #define NULL 0
    #else
        #define NULL ((void *)0)
    #endif
#endif

typedef long long 			LONGLONG;
typedef unsigned long long 	ULONGLONG;

typedef unsigned int		UINT;
typedef unsigned int        *PUINT;
typedef int					INT;
typedef int					BOOL;

typedef unsigned long		ULONG;
typedef ULONG				*PULONG;
typedef unsigned short		USHORT;
typedef USHORT*				PUSHORT;
typedef unsigned char		UCHAR;
typedef UCHAR*				PUCHAR;
typedef short				SHORT;
typedef unsigned char		byte;
typedef double				DOUBLE;
typedef float				FLOAT;




typedef int					*PINT;
typedef int					*LPINT;

typedef long				*LPLONG;

typedef void*				PVOID;
typedef const void*			LPCVOID;

#ifndef _WCHAR_DEFINED
#define _WCHAR_DEFINED
typedef wchar_t WCHAR;
typedef WCHAR OLECHAR;
typedef OLECHAR *BSTR;
typedef WCHAR* LPWSTR;
typedef const WCHAR* LPCWSTR;
#endif // !_WCHAR_DEFINED

typedef char CHAR;
typedef char* LPSTR;
typedef const char* LPCSTR;


#ifdef _UNICODE
	#define LPCTSTR LPCWSTR 
	#define _stprintf       _swprintf
	#define _T(x) L##x
	typedef wchar_t TCHAR;

#else	
	#define LPCTSTR LPCSTR
	#define _stprintf       sprintf
	#define _T(x)   x
	typedef char TCHAR;
#endif


#ifndef VOID
#define VOID 	void
typedef void*	LPVOID;
#endif // !VOID



#ifndef _BYTE_DEFINED
#define _BYTE_DEFINED
typedef byte		BYTE;
typedef BYTE		*PBYTE;
typedef BYTE		*LPBYTE;
#endif // !_BYTE_DEFINED

#ifndef _WORD_DEFINED
#define _WORD_DEFINED
typedef unsigned short WORD;
#endif // !_WORD_DEFINED


#ifndef _LONG_DEFINED
#define _LONG_DEFINED
typedef long LONG;
#endif // !_LONG_DEFINED

#ifndef _WPARAM_DEFINED
#define _WPARAM_DEFINED
typedef UINT_PTR WPARAM;
#endif // _WPARAM_DEFINED

#ifndef _DWORD_DEFINED
#define _DWORD_DEFINED
typedef unsigned long DWORD;
#endif // !_DWORD_DEFINED
#ifndef _LPARAM_DEFINED
#define _LPARAM_DEFINED
typedef LONG_PTR LPARAM;

#endif // !_LPARAM_DEFINED
#ifndef _LRESULT_DEFINED
#define _LRESULT_DEFINED
typedef LONG_PTR LRESULT;

#endif // !_LRESULT_DEFINED



typedef struct _FILETIME {
	DWORD dwLowDateTime;
	DWORD dwHighDateTime;
} FILETIME, *PFILETIME, *LPFILETIME;
typedef const FILETIME *PCFILETIME, *LPCFILETIME;


typedef void* 	HDC;
typedef void* 	HANDLE;
typedef void*	HMODULE;
typedef void*	HINSTANCE;
typedef void*	HTASK;
typedef void*	HKEY;
typedef void*	HDESK;
typedef void*	HMF;
typedef void*	HEMF;
typedef void*	HPEN;
typedef void*	HRSRC;
typedef void*	HSTR;
typedef void*	HWINSTA;
typedef void*	HKL;
typedef void*	HGDIOBJ;
typedef void*   HWND;
typedef HANDLE 	HDWP;

#ifndef _HFILE_DEFINED
#define _HFILE_DEFINED
typedef INT HFILE;

#endif // !_HFILE_DEFINED
#ifndef _LPWORD_DEFINED
#define _LPWORD_DEFINED
typedef WORD	*LPWORD;
typedef WORD	*PWORD;
#endif // !_LPWORD_DEFINED

#ifndef _LPDWORD_DEFINED
#define _LPDWORD_DEFINED
typedef DWORD *LPDWORD;
typedef DWORD*PDWORD;
#endif // !_LPDWORD_DEFINED


#ifndef _COLORREF_DEFINED
#define _COLORREF_DEFINED
typedef DWORD COLORREF;
#endif // !_COLORREF_DEFINED

#ifndef _LPCOLORREF_DEFINED
#define _LPCOLORREF_DEFINED
typedef DWORD *LPCOLORREF;
#endif // !_LPCOLORREF_DEFINED

typedef HANDLE *LPHANDLE;

typedef struct tagRECT
{
    LONG    left;
    LONG    top;
    LONG    right;
    LONG    bottom;
} RECT;

typedef struct tagRECT *PRECT;
typedef struct tagRECT *LRECT;

typedef struct _RECTL
{
    LONG left;
    LONG top;
    LONG right;
    LONG bottom;

}RECTL;

typedef struct _RECTL *PRECTL;
typedef struct _RECTL *LPRECTL;

typedef struct tagPOINT
{
	LONG x;
    LONG y;
} POINT;

typedef struct tagPOINT *PPOINT;

typedef struct tagPOINT *LPPOINT;

typedef struct _POINTL
{
    LONG x;
    LONG y;
}POINTL;
typedef struct _POINTL *PPOINTL;
typedef struct tagSIZE
{
	LONG cx;
	LONG cy;
} 	SIZE;

typedef struct tagSIZE *PSIZE;
typedef struct tagSIZE *LPSIZE;

typedef struct tagSIZEL
{
	LONG cx;
	LONG cy;
}SIZEL;

typedef struct tagSIZEL *PSIZEL;
typedef struct tagSIZEL *LPSIZEL;


typedef struct _LARGE_INTEGER
{
    LONGLONG QuadPart;
}LARGE_INTEGER;

typedef LARGE_INTEGER *PLARGE_INTEGER;

typedef struct _ULARGE_INTEGER
{
	ULONGLONG QuadPart;
} ULARGE_INTEGER;


typedef LONG SCODE;



#define DLL_PROCESS_ATTACH   1    
#define DLL_THREAD_ATTACH    2    
#define DLL_THREAD_DETACH    3    
#define DLL_PROCESS_DETACH   0   


//#ifndef _LINUX_LIMITS_H
//#define _LINUX_LIMITS_H
//#define NR_OPEN           1024
//#define NGROUPS_MAX		65536    /* supplemental group IDs are available */
//#define ARG_MAX			131072    /*  bytes of args + environ for exec() */
//#define LINK_MAX			127    /* # links a file may have */
//#define MAX_CANON			255    /* size of the canonical input queue */
//#define MAX_INPUT			255    /* size of the type-ahead buffer */
//#define NAME_MAX			255    /* */
//#define PATH_MAX			4096    /* */
//#define PIPE_BUF			4096    /*  bytes in atomic write to a pipe */
//#define XATTR_NAME_MAX	255    /*  chars in an extended attribute name */
//#define XATTR_SIZE_MAX	65536    /* size of an extended attribute value (64k) */
//#define XATTR_LIST_MAX	65536    /* size of extended attribute namelist (64k) */
//#define RTSIG_MAX			32
//#endif

#ifndef MAX_PATH
#define MAX_PATH			4096
#endif

#ifndef _MAX_PATH
#define _MAX_PATH			4096	
#endif

#endif
