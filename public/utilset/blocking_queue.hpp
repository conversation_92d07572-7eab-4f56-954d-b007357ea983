#pragma once
#include <queue>
#include <mutex>
#include <condition_variable>
#include <cstdint>
#include <atomic>
#include <chrono>
#include <functional>


template<typename T>
class BlockingQueue {
public:
	typedef std::function<void(const T&)> Callback;

	BlockingQueue() : data_queue_(), data_queue_mtx_(), data_queue_cond_(), data_max_size_(0) {}
	~BlockingQueue() {}

	void Push(const T& value) {
		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		data_queue_.push(value);
		data_queue_cond_.notify_all();
	}

	T Pop() {
		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		while (data_queue_.empty()) {
			data_queue_cond_.wait(lock);
		}
		T value = data_queue_.front();
		data_queue_.pop();
		return value;
	}

	T PopWaitFor(const std::chrono::seconds& second) {
		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		data_queue_cond_.wait_for(lock, second);
		if (!data_queue_.empty())
		{
			T value = data_queue_.front();
			data_queue_.pop();
			return value;
		}
		return T{};
	}

	int64_t GetSize() {
		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		return data_queue_.size();
	}

	void SetMaxSize(int64_t size) {
		if (0 >= size)
			return;
		data_max_size_ = size;
	}

	void AdjustQueueSize() {
		if (0 >= data_max_size_)
			return;

		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		auto curr_size = data_queue_.size();
		if (curr_size <= data_max_size_)
			return;

		auto drop_size = curr_size - data_max_size_;
		for (int64_t i = 0; i < drop_size; i++) {
			data_queue_.pop();
		}
	}

	void ForeachQueue(Callback cb) {
		std::unique_lock<std::mutex> lock(data_queue_mtx_);
		auto size = data_queue_.size();

		for (int i = 0; i < size; i++)
		{
			auto val = data_queue_.front();
			cb(val);
			data_queue_.push(data_queue_.front());
			data_queue_.pop();
		}
	}

private:
	std::queue<T> data_queue_;
	std::mutex data_queue_mtx_;
	std::condition_variable data_queue_cond_;
	std::atomic<int64_t> data_max_size_;
};