
#ifndef _MEMMAPFILE_INCLUDE_H_
#define _MEMMAPFILE_INCLUDE_H_
#ifdef _WIN32
#pragma once
#endif

#include <windows.h>
#include <stdlib.h>	// for _MAX_PATH
#include "globalevent.h"
#include "../listsessions.h"

namespace atsdk {

class CMemMapFile
{
public:
	CMemMapFile(DWORD dwGrowSize = 0);
	~CMemMapFile();
	
	//Methods
	BOOL    MapFile(LPCTSTR sFilename, BOOL bReadOnly = FALSE, 
					DWORD dwReservedSize = 0, BOOL bNamed = FALSE, 
					DWORD dwShareMode = FILE_SHARE_READ|FILE_SHARE_WRITE, 
					BOOL bAppendNull = FALSE, BOOL bGrowable = FALSE );
	BOOL    MapMemory(LPCTSTR sName, DWORD dwBytes, BOOL bReadOnly = FALSE);
	BOOL	MapExistingMemory(LPCTSTR sName, DWORD dwBytes, BOOL bReadOnly = FALSE);
	BOOL	MapExistingMemoryVista(int iTryCount=1);
	VOID    UnMap();
	VOID    CloseMap()
	{
		ReleaseMappedBuffer(m_lpData);
		m_lpData = NULL;
	}
	BOOL	Flush();

	inline	LPVOID GetMappedBuffer ()
	{
		return MapViewOfFile (m_hMapping, m_dwShareMode, 0, 0, 0);
	}
	inline	void ReleaseMappedBuffer (LPVOID pBuffer)
	{
		UnmapViewOfFile (pBuffer);
	}

	BOOL	BackupFile(LPCTSTR szBackupName);

	//Accessors
	inline	LPCTSTR	GetFileName() { return m_szFileName; };
	inline	LPCTSTR	GetMappingName() { return m_szMappingName; };
	inline	HANDLE  GetFileHandle() const { return m_hFile; };
	inline	HANDLE  GetFileMappingHandle() const { return m_hMapping; };
	inline	DWORD   GetLength() const { return m_dwLength; };
	inline	LPVOID	GetBuffer() { return m_lpData; };
	inline	BOOL    IsReset() const { return m_bReset; };
	
protected:  
	
	BOOL	MapHandle(HANDLE hHandle);
	LPCTSTR	CreateMappingName(LPCTSTR sName, BOOL bNamed);
	LPCTSTR	CreateMutexName();
	
	HANDLE  m_hFile;
	HANDLE  m_hMapping;
	BOOL    m_bReadOnly;
	BOOL    m_bAppendNull;
	LPVOID  m_lpData;
	TCHAR	m_szFileName[_MAX_PATH];
	TCHAR	m_szMappingName[_MAX_PATH];
	TCHAR	m_szMappingNameVista[_MAX_PATH];
	BOOL    m_bOpen;
	DWORD   m_dwLength;
	DWORD	m_dwShareMode;
	DWORD	m_dwGrowSize;
	BOOL	m_bReset;
	BOOL	m_IsNt;
	BOOL    m_bIs9X;
	BOOL	m_IsVista;

	CEnumSessions m_EnumSessions;
};

} //namespace atsdk

#endif // _MEMMAPFILE_INCLUDE_H_
