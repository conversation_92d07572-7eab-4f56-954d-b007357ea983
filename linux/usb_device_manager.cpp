// usb_device_manager.cpp
#include "usb_device_manager.h"

#include <fstream>
#include <algorithm>
#include <iomanip>
#include <stdexcept>
#include <cstring>
#include <unistd.h>
#include "qlogger.h"

namespace usb_manager {

UsbDeviceManager::UsbDeviceManager() : udev_ctx_(nullptr) {
  // Initialize libudev
  udev_ctx_ = udev_new();
  if (!udev_ctx_) {
    throw std::runtime_error("cannot init libudev");
  }

  InitializeVendorIds();
}

UsbDeviceManager::~UsbDeviceManager() {
  if (udev_ctx_) {
    udev_unref(udev_ctx_);
  }
}

void UsbDeviceManager::InitializeVendorIds() {
  // Initialize WiFi device vendor ID list
  wifi_vendor_ids_ = {
    "0bda",  // Realtek
    "148f",  // Ralink  
    "0cf3",  // Atheros
    "8086",  // Intel
    "0b05",  // ASUS
    "050d",  // Belkin
    "2357",  // TP-Link
    "0e8d",  // MediaTek
    "13b1",  // Linksys
    "07d1",  // D-Link
    "0846",  // Netgear
    "0411",  // Buffalo
    "7392"   // Edimax
  };

  // Initialize smartphone vendor ID list
  phone_vendor_ids_ = {
    "18d1",  // Google (Android)
    "05ac",  // Apple
    "04e8",  // Samsung  
    "2717",  // Xiaomi
    "12d1",  // Huawei
    "0bb4",  // HTC
    "22b8",  // Motorola
    "19d2",  // ZTE
    "1004",  // LG
    "0fce",  // Sony Ericsson
    "2a70",  // OnePlus
    "1bbb",  // T-Mobile
    "0489",  // Foxconn (iPhone)
    "17ef",  // Lenovo
    "0414"   // Gigaset
  };
}

std::vector<UsbDevice> UsbDeviceManager::ScanUsbDevices() {
  std::vector<UsbDevice> devices;
  
  struct udev_enumerate* enumerate = udev_enumerate_new(udev_ctx_);
  if (!enumerate) {
    QLogInfoUtf8(nullptr, "Failed to create udev enumerator");
    return devices;
  }
  
  // Set filter conditions: USB devices only
  udev_enumerate_add_match_subsystem(enumerate, "usb");
  udev_enumerate_add_match_property(enumerate, "DEVTYPE", "usb_device");

  // Scan devices
  udev_enumerate_scan_devices(enumerate);
  
  struct udev_list_entry* device_list = udev_enumerate_get_list_entry(enumerate);
  struct udev_list_entry* device_entry;
  
  // Iterate through all found devices
  udev_list_entry_foreach(device_entry, device_list) {
    const char* syspath = udev_list_entry_get_name(device_entry);
    struct udev_device* udev_device = udev_device_new_from_syspath(udev_ctx_, syspath);
    
    if (udev_device) {
      UsbDevice device = ParseUsbDevice(udev_device);
      if (!device.vendor_id.empty() && !device.product_id.empty()) {
        devices.push_back(device);
      }
      udev_device_unref(udev_device);
    }
  }
  
  udev_enumerate_unref(enumerate);
  return devices;
}

UsbDevice UsbDeviceManager::ParseUsbDevice(struct udev_device* udev_device) {
  UsbDevice device;

  // Get basic path information
  const char* syspath = udev_device_get_syspath(udev_device);
  const char* devpath = udev_device_get_devpath(udev_device);

  if (syspath) device.syspath = syspath;
  if (devpath) device.devpath = devpath;

  // Get device attributes
  device.vendor_id = GetDeviceAttribute(udev_device, "idVendor");
  device.product_id = GetDeviceAttribute(udev_device, "idProduct");
  device.device_class = GetDeviceAttribute(udev_device, "bDeviceClass");
  device.device_subclass = GetDeviceAttribute(udev_device, "bDeviceSubClass");
  device.device_protocol = GetDeviceAttribute(udev_device, "bDeviceProtocol");
  device.product_name = GetDeviceAttribute(udev_device, "product");
  device.manufacturer = GetDeviceAttribute(udev_device, "manufacturer");
  device.serial = GetDeviceAttribute(udev_device, "serial");
  device.bus_num = GetDeviceAttribute(udev_device, "busnum");
  device.dev_num = GetDeviceAttribute(udev_device, "devnum");

  // Read authorization status
  std::string auth_str = GetDeviceAttribute(udev_device, "authorized");
  device.authorized = (auth_str == "1");

  // Get interface information (for composite devices)
  GetInterfaceInfo(device);

  // Identify device type
  device.device_type = IdentifyDeviceType(device);
  
  return device;
}

std::string UsbDeviceManager::GetDeviceAttribute(struct udev_device* udev_device, 
                                                const char* attr_name) {
  const char* value = udev_device_get_sysattr_value(udev_device, attr_name);
  return value ? std::string(value) : std::string();
}

void UsbDeviceManager::GetInterfaceInfo(UsbDevice& device) {
  // Get interface information for composite devices
  struct udev_enumerate* enumerate = udev_enumerate_new(udev_ctx_);
  if (!enumerate) return;

  // Find all interfaces for this device
  udev_enumerate_add_match_subsystem(enumerate, "usb");
  udev_enumerate_add_match_property(enumerate, "DEVTYPE", "usb_interface");
  
  std::string parent_path = device.syspath;
  udev_enumerate_scan_devices(enumerate);
  
  struct udev_list_entry* interface_list = udev_enumerate_get_list_entry(enumerate);
  struct udev_list_entry* interface_entry;
  
  udev_list_entry_foreach(interface_entry, interface_list) {
    const char* syspath = udev_list_entry_get_name(interface_entry);
    
    // Check if this is an interface of the current device
    if (std::string(syspath).find(parent_path) == 0) {
      struct udev_device* interface_device = udev_device_new_from_syspath(udev_ctx_, syspath);
      if (interface_device) {
        std::string if_class = GetDeviceAttribute(interface_device, "bInterfaceClass");
        std::string if_subclass = GetDeviceAttribute(interface_device, "bInterfaceSubClass");
        
        if (!if_class.empty() && device.interface_class.empty()) {
          device.interface_class = if_class;
          device.interface_subclass = if_subclass;
        }
        
        udev_device_unref(interface_device);
        break;  // Get the first interface information only
      }
    }
  }
  
  udev_enumerate_unref(enumerate);
}

DeviceType UsbDeviceManager::IdentifyDeviceType(const UsbDevice& device) {
  try {
    // Convert hexadecimal strings to numeric values
    int dev_class = 0, dev_subclass = 0;
    int if_class = 0, if_subclass = 0;
    
    if (!device.device_class.empty()) {
      dev_class = std::stoi(device.device_class, nullptr, 16);
      dev_subclass = std::stoi(device.device_subclass, nullptr, 16);
    }
    
    if (!device.interface_class.empty()) {
      if_class = std::stoi(device.interface_class, nullptr, 16);
      if_subclass = std::stoi(device.interface_subclass, nullptr, 16);
    }
    
    // Mass Storage 大容量存储设备
    if (dev_class == 0x08 || if_class == 0x08) {
      if (dev_subclass == 0x06 || if_subclass == 0x06) {
        return DeviceType::kUsbStorage;
      }
      if (dev_subclass == 0x02 || dev_subclass == 0x05 || 
          if_subclass == 0x02 || if_subclass == 0x05) {
        return DeviceType::kCdromDevice;
      }
      return DeviceType::kStorageDevice;
    }
    
    // Communication Device Class
    if (dev_class == 0x02 || if_class == 0x02) {
      return DeviceType::kSmartphone;
    }
    
    // Check if it's a WiFi device
    if (IsWifiDevice(device)) {
      return DeviceType::kWifiAdapter;
    }

    // Check if it's a smartphone
    if (IsSmartphoneDevice(device)) {
      return DeviceType::kSmartphone;
    }

    // Hub device
    if (dev_class == 0x09) {
      if (IsSmartphoneDevice(device)) {
        return DeviceType::kSmartphone;
      }
      return DeviceType::kUsbHub;
    }

    // Composite device
    if (dev_class == 0x00 || dev_class == 0xEF) {
      return CheckCompositeDeviceType(device);
    }

  } catch (const std::exception& e) {
    // Ignore conversion errors
  }
  
  return DeviceType::kOtherDevice;
}

bool UsbDeviceManager::IsWifiDevice(const UsbDevice& device) {
  // Identify by vendor ID
  if (wifi_vendor_ids_.find(device.vendor_id) != wifi_vendor_ids_.end()) {
    return true;
  }

  // Identify by product name
  std::string product_lower = ToLowerCase(device.product_name);
  
  std::vector<std::string> wifi_keywords = {
    "wifi", "wireless", "wlan", "802.11", "wi-fi",
    "dongle", "adapter", "network", "rtl", "ralink"
  };
  
  for (const auto& keyword : wifi_keywords) {
    if (product_lower.find(keyword) != std::string::npos) {
      return true;
    }
  }
  
  return false;
}

bool UsbDeviceManager::IsSmartphoneDevice(const UsbDevice& device) {
  // Identify by vendor ID
  if (phone_vendor_ids_.find(device.vendor_id) != phone_vendor_ids_.end()) {
    return true;
  }

  // Identify by product name
  std::string product_lower = ToLowerCase(device.product_name);
  
  std::vector<std::string> phone_keywords = {
    "android", "iphone", "galaxy", "pixel", "xiaomi", 
    "huawei", "oppo", "vivo", "oneplus", "phone",
    "mobile", "smartphone", "adb"
  };
  
  for (const auto& keyword : phone_keywords) {
    if (product_lower.find(keyword) != std::string::npos) {
      return true;
    }
  }
  
  return false;
}

DeviceType UsbDeviceManager::CheckCompositeDeviceType(const UsbDevice& device) {
  // For composite devices, prioritize identification by vendor ID
  if (IsSmartphoneDevice(device)) {
    return DeviceType::kSmartphone;
  }
  
  if (IsWifiDevice(device)) {
    return DeviceType::kWifiAdapter;
  }
  
  return DeviceType::kCompositeDevice;
}

bool UsbDeviceManager::DisableDevice(const std::string& syspath) {
  std::string auth_file = syspath + "/authorized";
  
  std::ofstream file(auth_file);
  if (!file.is_open()) {
    QLogInfoUtf8(nullptr, "Failed to open authorization file: %s", auth_file.c_str());
    return false;
  }
  
  file << "0";
  file.close();
  
  return true;
}

bool UsbDeviceManager::EnableDevice(const std::string& syspath) {
  std::string auth_file = syspath + "/authorized";
  
  std::ofstream file(auth_file);
  if (!file.is_open()) {
    QLogInfoUtf8(nullptr, "Failed to open authorization file: %s", auth_file.c_str());
    return false;
  }
  
  file << "1";
  file.close();
  
  return true;
}

bool UsbDeviceManager::DisableDevice(const UsbDevice& device) {
  return DisableDevice(device.syspath);
}

bool UsbDeviceManager::EnableDevice(const UsbDevice& device) {
  return EnableDevice(device.syspath);
}

bool UsbDeviceManager::IsTargetDevice(const UsbDevice& device) {
  static const std::set<DeviceType> target_types = {
    DeviceType::kUsbStorage,
    DeviceType::kCdromDevice,
    DeviceType::kWifiAdapter,
    DeviceType::kSmartphone,
    DeviceType::kStorageDevice
  };
  
  return target_types.find(device.device_type) != target_types.end();
}

UsbDevice UsbDeviceManager::ParseUsbDeviceByInterface(struct udev_device* udev_device) {
  UsbDevice device;
  
  if (!udev_device) {
    return device;
  }
  
  // Get basic path information for interface device
  const char* syspath = udev_device_get_syspath(udev_device);
  const char* devpath = udev_device_get_devpath(udev_device);

  if (syspath) device.syspath = syspath;
  if (devpath) device.devpath = devpath;

  // Get interface attributes
  device.interface_class = GetDeviceAttribute(udev_device, "bInterfaceClass");
  device.interface_subclass = GetDeviceAttribute(udev_device, "bInterfaceSubClass");
  device.device_protocol = GetDeviceAttribute(udev_device, "bInterfaceProtocol");

  // Get parent device (USB device) information
  struct udev_device* parent_device = udev_device_get_parent_with_subsystem_devtype(
      udev_device, "usb", "usb_device");
  
  if (parent_device) {
    // Get device-level attributes from parent device
    device.vendor_id = GetDeviceAttribute(parent_device, "idVendor");
    device.product_id = GetDeviceAttribute(parent_device, "idProduct");
    device.device_class = GetDeviceAttribute(parent_device, "bDeviceClass");
    device.device_subclass = GetDeviceAttribute(parent_device, "bDeviceSubClass");
    device.product_name = GetDeviceAttribute(parent_device, "product");
    device.manufacturer = GetDeviceAttribute(parent_device, "manufacturer");
    device.serial = GetDeviceAttribute(parent_device, "serial");
    device.bus_num = GetDeviceAttribute(parent_device, "busnum");
    device.dev_num = GetDeviceAttribute(parent_device, "devnum");

    // Read authorization status
    std::string auth_str = GetDeviceAttribute(parent_device, "authorized");
    device.authorized = (auth_str == "1");

    // Update syspath to parent device path (for device control)
    const char* parent_syspath = udev_device_get_syspath(parent_device);
    if (parent_syspath) {
      device.syspath = parent_syspath;
    }
  }

  // Identify device type (based on interface and device information)
  device.device_type = IdentifyDeviceType(device);
  
  return device;
}

int UsbDeviceManager::DisableTargetDevices() {
  auto devices = ScanUsbDevices();
  int disabled_count = 0;
  
  QLogInfoUtf8(nullptr, "Starting to scan and disable target USB devices...");
  
  for (const auto& device : devices) {
    if (IsTargetDevice(device) && device.authorized) {

      if (!device.product_name.empty()) {
        QLogInfoUtf8(nullptr, "Disabling device: %s", device.product_name.c_str());
      } else {
        QLogInfoUtf8(nullptr, "Disabling device: Unknown device");
      }

      if (DisableDevice(device.syspath)) {
        disabled_count++;
        QLogInfoUtf8(nullptr, "  -> Device disabled successfully");
      } else {
        QLogInfoUtf8(nullptr, "  -> Failed to disable device");
      }
    }
  }
  
  return disabled_count;
}

int UsbDeviceManager::EnableTargetDevices() {
  auto devices = ScanUsbDevices();
  int enabled_count = 0;
  
  QLogInfoUtf8(nullptr, "Starting to scan and enable target USB devices...");
  
  for (const auto& device : devices) {
    if (IsTargetDevice(device) && !device.authorized) {
      
      if (!device.product_name.empty()) {
        QLogInfoUtf8(nullptr, "Enabling device: %s", device.product_name.c_str());
      } else {
        QLogInfoUtf8(nullptr, "Enabling device: Unknown device");
      }

      if (EnableDevice(device.syspath)) {
        enabled_count++;
        QLogInfoUtf8(nullptr, "  -> Device enabled successfully");
      } else {
        QLogInfoUtf8(nullptr, "  -> Failed to enable device");
      }
    }
  }
  
  return enabled_count;
}

std::string UsbDeviceManager::ToLowerCase(const std::string& str) {
  std::string result = str;
  std::transform(result.begin(), result.end(), result.begin(), ::tolower);
  return result;
}

std::string UsbDeviceManager::TruncateString(const std::string& str, size_t max_length) {
  if (str.length() > max_length) {
    return str.substr(0, max_length - 3) + "...";
  }
  return str;
}

}  // namespace usb_manager

