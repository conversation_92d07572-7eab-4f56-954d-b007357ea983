#ifndef _UTIL_BITS_HPP_
#define _UTIL_BITS_HPP_

#if TARGET_BITS == 64

	#if (TARGET_OS == OS_POSIX )
		typedef long long INT_PTR, *PINT_PTR;
		typedef unsigned long long UINT_PTR, *PUINT_PTR;
		typedef long long LONG_PTR, *PLONG_PTR;
		typedef unsigned long long ULONG_PTR, *PULONG_PTR;
	#elif (TARGET_OS == OS_DARWIN)
		typedef long long INT_PTR, *PINT_PTR;
		typedef unsigned long long UINT_PTR, *PUINT_PTR;
		typedef long long LONG_PTR, *PLONG_PTR;
		typedef unsigned long long ULONG_PTR, *PULONG_PTR;
	#endif

#else
	#if (TARGET_OS == OS_POSIX )
		typedef int INT_PTR, *PINT_PTR;
		typedef unsigned int UINT_PTR, *PUINT_PTR;
		typedef long LONG_PTR, *PLONG_PTR;
		typedef unsigned long ULONG_PTR, *PULONG_PTR;
	#elif (TARGET_OS == OS_DARWIN)
		typedef int INT_PTR, *PINT_PTR;
		typedef unsigned int UINT_PTR, *PUINT_PTR;
		typedef long LONG_PTR, *PLONG_PTR;
		typedef unsigned long ULONG_PTR, *PULONG_PTR;
	#endif
#endif


#endif
