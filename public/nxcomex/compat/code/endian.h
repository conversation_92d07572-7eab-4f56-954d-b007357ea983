#ifndef	_COMPAT_ENDIAN_H
#define	_COMPAT_ENDIAN_H

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

unsigned short	_read16_le(const unsigned char *buf);
unsigned int	_read32_le(const unsigned char *buf);
void			_write32_le(const int value, unsigned char *buffer);
void			_write64_le(const int64_t value, unsigned char *buffer);

#define			Big_endian		100
#define			Little_endian	200

int				_Is_BigLittle_Endian();

#ifdef	__cplusplus
}
#endif

#endif

