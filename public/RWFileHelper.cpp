#include "RWFileHelper.h"
#include "md5/md5.h"
#if defined(_WIN32) || defined(_WIN64)
#include "win/WinUtfHelp.h"
#pragma warning( disable : 4838 )
#pragma warning( disable : 4244 )
#pragma warning( disable : 4309 )
#else
#include <stdio.h>
#include <unistd.h>
#include  <string.h>    //strerror()
#include  <errno.h>     //errno
#include  <sys/types.h> //stat()
#include  <sys/stat.h>
#include <fcntl.h>
#endif
#include "sha/Sha256.h"

namespace RWFileHelper
{
	static std::string Bin2HString(const unsigned char* pbySrc, unsigned long ulSize)
	{
		char dst[65] = { 0 };

		unsigned char byTemp;
		unsigned long i = 0;
		for (i = 0; i < ulSize; i++)
		{
			byTemp = pbySrc[i] & 0x0F;
			if (byTemp < 0x0A)
				dst[(i << 1) + 1] = byTemp + 0x30;
			else
				dst[(i << 1) + 1] = byTemp + 0x37;

			byTemp = ((pbySrc[i] >> 4) & 0x0F);
			if (byTemp < 0x0A)
				dst[i << 1] = byTemp + 0x30;
			else
				dst[i << 1] = byTemp + 0x37;
		}
		dst[i << 1] = '\0';

		return std::string(dst);
	}

	std::string CalcFileMD5(FILE* f)
	{
		if (!f)
			return std::string();

		fseek(f, 0, SEEK_SET);

		md5_byte_t md5_buf[16];
		md5_state_s mst;
		md5_init(&mst);

		while (!feof(f)) {

			char buf[1024] = { 0 };
			size_t readSize = fread(buf, 1, 1024, f);
			if (readSize != 1024) {
				if (ferror(f))
					return std::string();
			}

			md5_append(&mst, (const md5_byte_t*)buf, (int)readSize);
		}
		md5_finish(&mst, md5_buf);

		std::string md5 = Bin2HString(md5_buf, 16);
		return md5;
	}

	std::string CalcFileMD5(const std::string& filePath)
	{
		FILE* f = RWFileHelper::OpenFile(filePath, false);
		if (f)
		{
			auto res = RWFileHelper::CalcFileMD5(f);
			fclose(f);

			return res;
		}
			
		return std::string();
	}

	std::string CalcStringMD5(const std::string& str)
	{
		md5_byte_t md5_buf[16];
		md5_state_s mst;
		md5_init(&mst);
		md5_append(&mst, (const md5_byte_t*)str.c_str(), (int)str.size());
		md5_finish(&mst, md5_buf);

		std::string md5 = Bin2HString(md5_buf, 16);
		return md5;
	}

	FILE* OpenFile(const std::string& filePath, bool bWrite, bool bAppendWrite)
	{
		if (!filePath.size())
			return nullptr;
#if defined(_WIN32) || defined(_WIN64)
		std::wstring win_path = winHelper::U8ToU16(filePath.c_str());

		FILE* f = nullptr;

		const wchar_t* mode = bWrite ? (bAppendWrite? L"ab":L"wb") : L"rb";

		f = _wfsopen(win_path.c_str(), mode, bWrite?_SH_DENYWR:_SH_DENYNO);
		if (!f)
			return nullptr;
		return f;
#else
		if (!bWrite) {
			struct stat buf;
			if (-1 == stat(filePath.c_str(), &buf) || !(buf.st_mode & S_IFREG)) {
				return nullptr;
			}
		}

		const char* mode = bWrite ? (bAppendWrite ? "ab" : "wb") : "rb";

		FILE* f = fopen(filePath.c_str(), mode);
		if (!f)
			return nullptr;
		return f;
#endif
	}
	std::string ReadFileBinContent(const char* filePath)
	{
		FILE* f = OpenFile(filePath, false);
		if (!f)
			return std::string();

		fseek(f, 0, SEEK_SET);

		std::string ret;

		while (!feof(f)) {

			char buf[1024] = { 0 };
			size_t readSize = fread(buf, 1, 1024, f);
			if (readSize != 1024) {
				if (ferror(f)) {
					fclose(f);
					return std::string();
				}					
			}
			if(readSize){
				ret.append(buf, readSize);
			}	
		}

		fclose(f);
		return ret;
	}

	std::string ReadFileBinContentW(const wchar_t* filePath)
	{
#if defined(_WIN32) || defined(_WIN64)
		FILE* f = nullptr;
		errno_t err = _wfopen_s(&f, filePath, L"rb");
		if (err || !f)
			return std::string();

		fseek(f, 0, SEEK_SET);

		std::string ret;

		while (!feof(f)) {

			char buf[1024] = { 0 };
			size_t readSize = fread(buf, 1, 1024, f);
			if (readSize != 1024) {
				if (ferror(f)) {
					fclose(f);
					return std::string();
				}
			}
			if(readSize){
				ret.append(buf, readSize);
			}			
		}

		fclose(f);
		return ret;
#else
		return std::string();
#endif
	}

	bool  MoveToFind(FILE* f, const std::string& sub)
	{
		int size = (int)sub.size();
		if (!size)
			return false;
		char* p = new (std::nothrow) char[size];
		
		int offset = 0;
		while (!feof(f)) {

			fseek(f, offset, SEEK_SET);
			size_t readSize = fread(p, 1, size, f);
			if (readSize != size) {
				delete[] p;
				return false;
			}		
			if (memcmp(p, sub.c_str(), size) == 0) {
				delete[] p;
				return true;
			}
			offset++;
		}
		delete[] p;
		return false;
	}

	unsigned int GetFileSize(const std::string& filepath)
	{
		if (filepath.empty())
			return 0;
#if defined(_WIN32) || defined(_WIN64)
		WIN32_FILE_ATTRIBUTE_DATA ws;
		std::wstring win_path = winHelper::U8ToU16(filepath.c_str());
		if(GetFileAttributesEx(win_path.c_str(), GetFileExInfoStandard, &ws))
			return ws.nFileSizeLow;
		return 0;
#else
		struct stat buf;
		auto res = stat(filepath.c_str(), &buf);
		if (res == -1)
			return 0;
		return buf.st_size;
#endif
	}

	bool WriteFileBinContent(const char* filePath, const char* binContent)
	{
		FILE* f = OpenFile(filePath, true);
		if (!f)
			return false;

		fwrite(binContent, sizeof(char), strlen(binContent), f);
		fclose(f);

		return true;
	}

	bool CpyFile(FILE* src, FILE* dst)
	{
		const int bufSize = 1024;
		char buf[bufSize] = { 0 };
		while (!feof(src)) {
			
			size_t readSize = fread(buf, 1, bufSize, src);
			if (readSize != bufSize) {
				if (ferror(src)) {
					return false;
				}
			}
			
			auto writeSize = fwrite(buf, 1, readSize, dst);
			if (writeSize != readSize)
				return false;
		}
		return true;
	}

	bool CpyFile(const std::string& src, const std::string& dst)
	{
		auto* s = OpenFile(src, false);
		auto* d = OpenFile(dst, true);
		bool res = false;
		if (s && d)	{
			res = CpyFile(s, d);
		}
		if (s)
			fclose(s);
		if (d)
			fclose(d);

		return res;
	}

	bool DelFile(const std::string& file)
	{
		if (!file.size())return false;
#ifdef _WIN32
		std::wstring win_path = winHelper::U8ToU16(file.c_str());
		return ::DeleteFile(win_path.c_str())?true:false;
#else
		return unlink(file.c_str()) == 0;
#endif
	}

	bool LinkFile(const std::string& head, const std::string& tail)  // head + tail => headtail 
	{
		auto* f1 = OpenFile(head, true, true);
		auto* f2 = OpenFile(tail, false);
		if (!f1 || !f2) {
			if (f1)fclose(f1);
			if (f2)fclose(f2);
			return false;
		}

		if (fseek(f1, 0, SEEK_END) != 0) {
			fclose(f1);
			fclose(f2);
			return false;
		}

		bool haveError = false;
		const int buf_size = 1024;
		char buf[buf_size] = { 0 };
		while (!feof(f2)) {

			memset(buf, 0, sizeof(buf));
			size_t readSize = fread(buf, 1, buf_size, f2);
			if (readSize != buf_size) {
				if (ferror(f2)) {
					haveError = true;
					break;
				}
			}

			auto writeSize = fwrite(buf, 1, readSize, f1);
			if (writeSize < readSize) {
				haveError = true;
				break;
			}
		}
		
		fclose(f1);
		fclose(f2);
		return !haveError;
	}
};
