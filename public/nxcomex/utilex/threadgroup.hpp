#ifndef _UTILEX_THREADGROUP_HPP_
#define _UTILEX_THREADGROUP_HPP_

#include <utilex/crt.hpp>

#ifdef __cplusplus

	class ithreadgroup
	{
	public:
		ithreadgroup() {

		
		}
		virtual ~ithreadgroup() {

		}
		int init_threads() {

			return init_threadpool(&pool);
		}
		int uninit_threads() {

			return uinit_threadpool(&pool);
		}
		int start_threads() {
		
			return start_threadpool(&pool);
		}

		int stop_threads() {

			return stop_threadpool(&pool);
		}

	protected:
		virtual void threads_run() PURE;
		virtual void threads_start() PURE;
		virtual	void threads_error() PURE;
		virtual void threads_stop() PURE;
	private:
		int	count;
		_threadpool_t	pool;
	};
#endif


#endif
