#ifndef _UTILEX_LOCKLIST_HPP_
#define _UTILEX_LOCKLIST_HPP_

#include <utilex/crt.hpp>

template<typename T>
class CLockList : public CAutoLock
{
public:
	CLockList() {
		_list_init(&m_list);
		m_count = 0;
	}
	~CLockList() {

	}
public:
	std_method(push_back)(T* pNode) {

		rc_assert(pNode != NULL, E_FAIL);
		SYNC_OBJ(this);

		_list_insert(&m_list, &pNode->list, _list_begin(&m_list));

		return S_OK;

	}

	std_method(is_empty)() {

		SYNC_OBJ(this);
		HRESULT hr = S_OK;
		return hr;
	}
	std_method(clear)() {

		SYNC_OBJ(this);

		return S_OK;
	}
	std_method(dump)() {

		SYNC_OBJ(this);

		return S_OK;
	}
	std_method_(UINT, get_list_size)() {
		return m_count;
	}
private:
	_list_s		m_list;
	int			m_count;

};


#endif
