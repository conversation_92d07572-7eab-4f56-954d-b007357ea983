#ifndef _COMPAT_MD5_H_
#define _COMPAT_MD5_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

typedef struct {
	unsigned int    count[2];
	unsigned int    state[4];
	unsigned char   buffer[64];
} MD5_CTX;

void MD5Init(MD5_CTX *ctx);
void MD5Update(MD5_CTX *ctx, unsigned char *input, unsigned int inputlen);
void MD5Final(MD5_CTX *ctx, unsigned char digest[16]);


#ifdef	__cplusplus
}
#endif


#endif 

