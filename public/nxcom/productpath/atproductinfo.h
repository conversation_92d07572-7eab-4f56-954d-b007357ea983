#pragma once

#include <productpath/atpathbase.h>
#include <productpath/atproductinfodef.h>

static LPCTSTR GetProductType(LPTSTR lpBuff, DWORD nMaxSize, LPCTSTR lpInstallPath = NULL, LPCTSTR lpVerDll = PRODUCTINFO_DLL)
{
	if( !lpBuff || !lpVerDll || 0x00 == (*lpVerDll) )
		return NULL;

	ZeroMemory(lpBuff, sizeof(TCHAR) * nMaxSize);

	TCHAR szPath[MAX_PATH*2];
	ZeroMemory(szPath, sizeof(TCHAR) * MAX_PATH*2);
	if(lpInstallPath && (*lpInstallPath))
	{
		_stprintf_s(szPath, MAX_PATH*2, _T("%s\\%s"), lpInstallPath, lpVerDll);
	}
	else
	{
		_stprintf_s(szPath, MAX_PATH*2, _T("%s"), lpVerDll);
	}

	HMODULE hModule = LoadLibrary(szPath);
	if(hModule)
	{
		pGetType p = (pGetType)GetProcAddress(hModule, PRODUCTINFO_TYPEPROC);
		if(p)
		{
			LPCTSTR lpType = p();
			_tcsncpy_s(lpBuff, nMaxSize, lpType, min(_tclen(lpType), nMaxSize-1));
		}
		FreeLibrary(hModule);
		hModule = NULL;
	}

	return lpBuff;
}

static LPCTSTR GetProductVersion(LPTSTR lpBuff, DWORD nMaxSize, LPCTSTR lpInstallPath = NULL, LPCTSTR lpVerDll = PRODUCTINFO_DLL)
{
	if( !lpBuff || !lpVerDll || 0x00 == (*lpVerDll) )
		return NULL;

	ZeroMemory(lpBuff, sizeof(TCHAR) * nMaxSize);

	TCHAR szPath[MAX_PATH*2];
	ZeroMemory(szPath, sizeof(TCHAR) * MAX_PATH*2);
	if(lpInstallPath && (*lpInstallPath))
	{
		_stprintf_s(szPath, MAX_PATH*2, _T("%s\\%s"), lpInstallPath, lpVerDll);
	}
	else
	{
		_stprintf_s(szPath, MAX_PATH*2, _T("%s"), lpVerDll);
	}

	HMODULE hModule = LoadLibrary(szPath);
	if(hModule)
	{
		pGetVersion p = (pGetVersion)GetProcAddress(hModule, PRODUCTINFO_VERPROC);
		if(p)
		{
			LPCTSTR lpVer = p();
			_tcsncpy_s(lpBuff, nMaxSize, lpVer, min(_tclen(lpVer), nMaxSize-1));
		}
		FreeLibrary(hModule);
		hModule = NULL;
	}

	return lpBuff;
}