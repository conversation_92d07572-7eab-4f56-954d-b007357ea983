#ifndef _UTIL_COMMON_HPP_
#define _UTIL_COMMON_HPP_

#ifndef _OFF_T_DEFINED
    #define _OFF_T_DEFINED
    typedef long _off_t; 
#endif


#ifdef CPP_NO_VTABLE
#undef CPP_NO_VTABLE
#endif

#if defined(_MSC_VER)
#define CPP_NO_VTABLE __declspec(novtable)
#else
#define CPP_NO_VTABLE
#endif


#if (OS_COMPILER == CC_MSVC )
	#define __attribute__(x)
#elif (OS_COMPILER == CC_GCC )

#endif


#if(OS_COMPILER == CC_MSVC)
	#define _aligned(g)		__declspec(align(g))
#else
	#define _aligned(g)		__attribute__((aligned(g)))
#endif

#define __aligned(g, type) _aligned(g) type


#define	PACK_ONEBYTE		__attribute__((packed))
#define	PACK_EIGHTBYTE		__attribute__((aligned(8))) 


#if (TARGET_OS == OS_WINDOWS)
	#define  _unused 
	#define OS_VISIBILITY_DEFAULT
	#define OS_VISIBILITY_HIDDEN
#elif (TARGET_OS == OS_POSIX)
	#define	_unused	__attribute__ ((__unused__))
		#ifdef __GNUC__
			#define OS_VISIBILITY_DEFAULT __attribute__ ((visibility ("default")))
			#define OS_VISIBILITY_HIDDEN   __attribute__ ((visibility ("hidden")))
		#endif
#elif (TARGET_OS == OS_DARWIN)
	#define	_unused	__attribute__ ((__unused__))
		#ifdef __GNUC__
			#define OS_VISIBILITY_DEFAULT __attribute__ ((visibility ("default")))
			#define OS_VISIBILITY_HIDDEN   __attribute__ ((visibility ("hidden")))
		#endif
#endif



#if (TARGET_OS == OS_WINDOWS)
	#define path_slash _T("\\")
#elif (TARGET_OS == OS_POSIX)
	#define path_slash _T("/")
#elif (TARGET_OS == OS_DARWIN)
	#define path_slash _T("/")
#endif




#ifdef __cplusplus
	#ifndef EXTERN_C
	#define EXTERN_C   extern "C"
	#endif


	#define static_inline inline

	#ifndef	interface
	#define interface 	struct
	#endif

#else
	#ifndef EXTERN_C
	#define EXTERN_C   extern
	#endif

	#if (TARGET_OS == OS_WINDOWS)
		#define inline _inline
	#endif

	#define static_inline static inline

	#ifndef	interface
	#define interface 	typedef struct
	#endif

	#ifdef CONST_VTABLE
	#define CONST_VTBL const
	#else
	#define CONST_VTBL
	#endif

#endif

#ifndef __cplusplus

#define bool	_Bool
#define false	0
#define true	1

#endif /* __cplusplus */






#define OS_HIDDEN_(type)                        OS_VISIBILITY_HIDDEN type
#define OS_EXTERNAL_VIS_(type)                  OS_VISIBILITY_DEFAULT type

#define OS_HIDDEN                               OS_VISIBILITY_HIDDEN
#define OS_EXTERNAL_VIS                         OS_VISIBILITY_DEFAULT

#undef  IMETHOD_VISIBILITY
#define IMETHOD_VISIBILITY                      OS_VISIBILITY_HIDDEN




#if (TARGET_OS == OS_WINDOWS)

	#define OS_STDCALL                              WINAPI
	#define OS_IMPORT                               __declspec(dllimport)
	#define OS_IMPORT_(type)                        type __declspec(dllimport) __stdcall
	#define OS_EXPORT                               __declspec(dllexport)
	#define OS_EXPORT_(type)                        type __declspec(dllexport) __stdcall


	#define OS_CALLBACK_(_type, _name)              _type (__stdcall * _name)
	#define OS_EXPORT_STATIC_MEMBER_(type)          type
	#define OS_IMPORT_STATIC_MEMBER_(type)          type

	#define MODULE_API                              HRESULT		OS_STDCALL 
	#define STD_METHOD(method)						EXTERN_C method		OS_STDCALL 

#elif (TARGET_OS == OS_POSIX)

	#define OS_STDCALL								
	#define OS_IMPORT                               NS_EXTERNAL_VIS
	#define OS_IMPORT_(type)                        NS_EXTERNAL_VIS_(type)
	#define OS_EXPORT                               NS_EXTERNAL_VIS
	#define OS_EXPORT_(type)                        NS_EXTERNAL_VIS_(type)

	#define OS_CALLBACK_(_type, _name)              _type (* _name)
	#define OS_EXPORT_STATIC_MEMBER_(type)          NS_EXTERNAL_VIS_(type)
	#define OS_IMPORT_STATIC_MEMBER_(type)          NS_EXTERNAL_VIS_(type)

	#define MODULE_API                              HRESULT    OS_VISIBILITY_DEFAULT 
	#define STD_METHOD(method)						EXTERN_C   OS_VISIBILITY_DEFAULT method

#elif (TARGET_OS == OS_DARWIN)

	#define OS_STDCALL								
	#define OS_IMPORT                               NS_EXTERNAL_VIS
	#define OS_IMPORT_(type)                        NS_EXTERNAL_VIS_(type)
	#define OS_EXPORT                               NS_EXTERNAL_VIS
	#define OS_EXPORT_(type)                        NS_EXTERNAL_VIS_(type)

	#define OS_CALLBACK_(_type, _name)              _type (* _name)
	#define OS_EXPORT_STATIC_MEMBER_(type)          NS_EXTERNAL_VIS_(type)
	#define OS_IMPORT_STATIC_MEMBER_(type)          NS_EXTERNAL_VIS_(type)

	#define MODULE_API                              HRESULT     
	#define MODULE_API_                             HRESULT  
	#define STD_METHOD(method)						EXTERN_C   OS_VISIBILITY_DEFAULT method

#endif

#define STD_COM_EXPORTS							EXTERN_C   MODULE_API


#if (TARGET_OS == OS_WINDOWS)
	#define Memory_Allocate(p)			HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY , p);
	#define Memory_Reallocate(p,size)	HeapReAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY,p,size);
	#define Memory_Free(p)				HeapFree(GetProcessHeap(), 0, p);
#elif (TARGET_OS == OS_POSIX)
	#define Memory_Allocate		malloc
	#define Memory_Reallocate   realloc
	#define Memory_Free(p)		free
#elif (TARGET_OS == OS_DARWIN)
	#define Memory_Allocate		malloc
	#define Memory_Reallocate   realloc
	#define Memory_Free(p)		free
#endif



typedef unsigned char   u_char;
typedef unsigned short  u_short;
typedef unsigned int    u_int;
typedef unsigned long   u_long;


#if (TARGET_OS == OS_WINDOWS)
typedef __int8  int8;
typedef __int16 int16;
typedef __int32 int32;
typedef __int64 int64;

typedef unsigned __int8  uint8;
typedef unsigned __int16 uint16;
typedef unsigned __int32 uint32;
typedef unsigned __int64 uint64;
#elif (TARGET_OS == OS_POSIX)
typedef int8_t  int8;
typedef int16_t int16;
typedef int32_t int32;
typedef int64_t int64;

typedef uint8_t  uint8;
typedef uint16_t uint16;
typedef uint32_t uint32;
typedef uint64_t uint64;
#elif (TARGET_OS == OS_DARWIN)

typedef int8_t  int8;
typedef int16_t int16;
typedef int32_t int32;
typedef int64_t int64;

typedef uint8_t  uint8;
typedef uint16_t uint16;
typedef uint32_t uint32;
typedef uint64_t uint64;

#endif




#endif
