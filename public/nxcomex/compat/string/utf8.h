#ifndef _COMPAT_UTF8_H_
#define _COMPAT_UTF8_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

	int is_utf8(const char* str, size_t length);

	int utf8_to_ucs2(const char *in,
		size_t *inbytes,
		wchar_t *out,
		size_t *outwords);

	int ucs2_to_utf8(const wchar_t *in,
		size_t *inwords,
		char *out,
		size_t *outbytes);

	//malloc you must free ptr
	char* ucs2_to_char(const wchar_t *in, size_t len);

	//malloc you must free ptr
	wchar_t* char_to_ucs2(const char *in, size_t len);

#ifdef	__cplusplus
}
#endif

#endif 


