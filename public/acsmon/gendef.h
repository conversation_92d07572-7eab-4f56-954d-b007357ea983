#ifndef _SYSMOM_GENDEF_H_
#define _SYSMON_GENDEF_H_

#ifndef EXTERN_C
#ifdef __cplusplus
#define EXTERN_C       extern "C"
#else
#define EXTERN_C       extern
#endif
#endif

#ifndef EXTERN_C_START
#ifdef __cplusplus
#define EXTERN_C_START extern "C" {
#define EXTERN_C_END   }
#else
#define EXTERN_C_START
#define EXTERN_C_END
#endif
#endif



#if defined(_MSC_VER)
#if !defined(BOOST_HAS_MS_INT64)
typedef signed char int8_t;
typedef unsigned char uint8_t;
typedef short int16_t;
typedef unsigned short uint16_t;
typedef int int32_t;
typedef unsigned int uint32_t;
typedef long long int64_t;
typedef unsigned long long uint64_t;
#endif
typedef unsigned long ptr32_t;
typedef unsigned long long ptr64_t;

#endif

#ifdef __LINUX__

# ifndef __KERNEL__
#  include <stdint.h>
# endif

#include <linux/types.h>
# ifdef __KERNEL__
#include <linux/version.h>
#if LINUX_VERSION_CODE < KERNEL_VERSION(5, 15, 0)
#include<stddef.h>
#endif
#else
#include<stddef.h>
#endif

#define UNREFERENCED_PARAMETER(x)

//#define wchar_t char

typedef void* PVOID;
typedef unsigned long ULONG;
typedef ULONG* PULONG;
typedef unsigned long long ULONGLONG;

#define DEFINE_GUID(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) \
         const struct _GUID name \
                = { l, w1, w2, { b1, b2,  b3,  b4,  b5,  b6,  b7,  b8 } }

typedef long NTSTATUS;
typedef unsigned short USHORT;
typedef USHORT* PUSHORT;

typedef uint64_t UINT64;
typedef uint32_t UINT32;
typedef uint16_t UINT16;
typedef uint8_t UINT8;

typedef unsigned long DWORD;
typedef void*	HANDLE;
typedef size_t SIZE_T;
typedef int BOOL;
typedef char* PCHAR;
typedef long long LONGLONG;
typedef long HRESULT;
//typedef void VOID;
#ifndef VOID
#define VOID 	void
typedef void* LPVOID;
#endif // !VOID
typedef unsigned char UCHAR;

#if defined(_WIN64)

typedef long long int LONG_PTR, *PLONG_PTR;
typedef unsigned long long int ULONG_PTR, *PULONG_PTR;

#else

typedef  long long LONG_PTR, *PLONG_PTR;
typedef  unsigned long long ULONG_PTR, *PULONG_PTR;

#endif


#define TRUE  1
#define FALSE 0
#define STATUS_SUCCESS 0

#define _TSTR(x) x

typedef char  kchar;
#define kstring string

#define KS "%s"

#define kstrlen strlen
#define kstrcpy strcpy
#define kstrncpy strncpy
#define kstrchr strchr
#define ksprintf_s(a,b,c,d)  sprintf(a,c,d)
#define ksprintf  sprintf
#define kstrncmp  strncmp
#define kstrcmp   strcmp
#else  //windows

#include<stddef.h>

#define _TSTR(x) L##x

typedef wchar_t kchar;
#define kstring wstring

#define KS "%ws"

#define kstrlen wcslen
#define kstrcpy wcscpy
#define kstrncpy wcsncpy
#define kstrchr wcschr
#define ksprintf_s(a,b,c,d) swprintf_s(a,b,c,d)
#define ksprintf swprintf
#define kstrncmp wcsncmp
#define kstrcmp  wcscmp
#endif

/* boolean */
typedef long bool_t;

#ifndef __LINUX__
/* process id */
#ifndef __pid_t_defined
typedef uint64_t pid_t;
#endif

#else

typedef long LONG;

#ifndef __pid_t_defined
typedef int pid_t;
# define __pid_t_defined
#endif

#endif

typedef void *handle_t;

#define mkptr32(ptr) (ptr32_t)(uintptr_t)(ptr)
#define mkptr64(ptr) (ptr64_t)(uintptr_t)(ptr)
#define mku32(val) (uint32_t)(uintptr_t)(val)
#define mku64(val) (uint64_t)(uintptr_t)(val)
#define mkpid(val) (pid_t)(uintptr_t)(val)

//#if !defined(__linux__) || !defined(__KERNEL__)
//# include <stddef.h>
//# include <sys/types.h>
//#endif

#ifndef __cplusplus
# define true 1L
# define false 0L
#endif

/* Size Constants */
#define _1kb	0x00000400
#define _4kb	0x00001000
#define _32kb	0x00008000
#define _64kb	0x00010000
#define _128kb	0x00020000
#define _256kb	0x00040000
#define _512kb	0x00080000
#define _1mb	0x00100000
#define _2mb	0x00200000
#define _4mb	0x00400000
#define _1gb	0x40000000
#define _2gb32	0x80000000U
#define _2gb	0x0000000080000000LL
#define _4gb	0x0000000100000000LL
#define _1tb	0x0000010000000000LL
#define _1pb	0x0004000000000000LL
#define _1eb	0x1000000000000000LL
#define _2eb	0x2000000000000000ULL

/* handle unreferenced variables */
#define __unreferenced(x) x

#ifdef _MSC_VER
//# define __inline__ __forceinline
# define __inline__ __inline
#else
#ifndef __inline__
#define __inline__ static inline
#endif
#endif


#ifdef __LINUX__
#define	debug_break() asm("int $3\n")
#else
#define	debug_break()	__debugbreak()
#endif


#endif /* _SYSMON_GENDEF_H_ */
