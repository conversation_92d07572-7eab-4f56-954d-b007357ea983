#ifndef _COM_INC_H_
#define _COM_INC_H_

#define DYNAMIC_NAME_LEN 256

//////////////////////////////////////////////////////////////////////////
#include <utilex/util.h>
//////////////////////////////////////////////////////////////////////////
#include <dlcom/ibase.h>
#include <dlcom/imsg.h>
#include <dlcom/icombase.h>
#include <dlcom/iplugin.h>
#include <dlcom/comsentry.hpp>
#include <dlcom/comfunc.hpp>
#include <dlcom/comfactory.hpp>
uvStdComNameSpace
//////////////////////////////////////////////////////////////////////////

#endif	

