#ifndef _COMPAT_ARGV_H_
#define _COMPAT_ARGV_H_

#include <compat/define.h>
#include <compat/error.h>
#include <compat/stdlib/iterator.h>

#ifdef	__cplusplus
extern "C" 
{
#endif





typedef	struct argv_s argv_t;

struct argv_s {
	int     len;			
	int     argc;
	char	**argv;
	void	(*push_back)(argv_t*, const char*);
	void	(*push_front)(argv_t*, const char*);
	char*	(*pop_back)(argv_t*);
	char*	(*pop_front)(argv_t*);
	void*	(*iter_head)(ITER*, argv_t*);
	void*	(*iter_next)(ITER*, argv_t*);
	void*	(*iter_tail)(ITER*, argv_t*);
	void*	(*iter_prev)(ITER*, argv_t*);
	argv_t* (*argv_split)(argv_t*, const char *, const char *);
	void	(*argv_terminate)(argv_t*);
	int		(*argv_size)(argv_t*);
};

#define ARGV_END	((char *) 0)



int		argv_init(argv_t* argvp, int size);
void	argv_clean(argv_t *argvp);


# ifdef	__cplusplus
}
# endif

#endif

