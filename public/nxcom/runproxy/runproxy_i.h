

/* this ALWAYS GENERATED file contains the definitions for the interfaces */


 /* File created by MIDL compiler version 7.00.0555 */
/* at Fri Oct 22 13:25:33 2010
 */
/* Compiler settings for .\runproxy.idl:
    Oicf, W1, Zp8, env=Win32 (32b run), target_arch=X86 7.00.0555 
    protocol : dce , ms_ext, c_ext, robust
    error checks: stub_data 
    VC __declspec() decoration level: 
         __declspec(uuid()), __declspec(selectany), __declspec(novtable)
         DECLSPEC_UUID(), MIDL_INTERFACE()
*/
/* @@MIDL_FILE_HEADING(  ) */

#pragma warning( disable: 4049 )  /* more than 64k source lines */


/* verify that the <rpcndr.h> version is high enough to compile this file*/
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error this stub requires an updated version of <rpcndr.h>
#endif // __RPCNDR_H_VERSION__

#ifndef COM_NO_WINDOWS_H
#include "windows.h"
#include "ole2.h"
#endif /*COM_NO_WINDOWS_H*/

#ifndef __runproxy_i_h__
#define __runproxy_i_h__

#if defined(_MSC_VER) && (_MSC_VER >= 1020)
#pragma once
#endif

/* Forward Declarations */ 

#ifndef __IProcessManager_FWD_DEFINED__
#define __IProcessManager_FWD_DEFINED__
typedef interface IProcessManager IProcessManager;
#endif 	/* __IProcessManager_FWD_DEFINED__ */


#ifndef ___IProcessManagerEvents_FWD_DEFINED__
#define ___IProcessManagerEvents_FWD_DEFINED__
typedef interface _IProcessManagerEvents _IProcessManagerEvents;
#endif 	/* ___IProcessManagerEvents_FWD_DEFINED__ */


#ifndef __ProcessManager_FWD_DEFINED__
#define __ProcessManager_FWD_DEFINED__

#ifdef __cplusplus
typedef class ProcessManager ProcessManager;
#else
typedef struct ProcessManager ProcessManager;
#endif /* __cplusplus */

#endif 	/* __ProcessManager_FWD_DEFINED__ */


/* header files for imported files */
#include "oaidl.h"
#include "ocidl.h"

#ifdef __cplusplus
extern "C"{
#endif 


#ifndef __IProcessManager_INTERFACE_DEFINED__
#define __IProcessManager_INTERFACE_DEFINED__

/* interface IProcessManager */
/* [unique][helpstring][nonextensible][dual][uuid][object] */ 


EXTERN_C const IID IID_IProcessManager;

#if defined(__cplusplus) && !defined(CINTERFACE)
    
    MIDL_INTERFACE("80E87323-B228-4777-9E93-140F586A8240")
    IProcessManager : public IDispatch
    {
    public:
        virtual /* [helpstring][id] */ HRESULT STDMETHODCALLTYPE RsCreateProcess( 
            /* [in] */ ULONG dwOwnerPid,
            /* [in] */ ULONG dwSessionId,
            /* [in] */ BSTR strMainPath,
            /* [in] */ BSTR strCmdPara,
            /* [in] */ BSTR strWorkPath,
            /* [in] */ ULONG dwType) = 0;
        
    };
    
#else 	/* C style interface */

    typedef struct IProcessManagerVtbl
    {
        BEGIN_INTERFACE
        
        HRESULT ( STDMETHODCALLTYPE *QueryInterface )( 
            IProcessManager * This,
            /* [in] */ REFIID riid,
            /* [annotation][iid_is][out] */ 
            __RPC__deref_out  void **ppvObject);
        
        ULONG ( STDMETHODCALLTYPE *AddRef )( 
            IProcessManager * This);
        
        ULONG ( STDMETHODCALLTYPE *Release )( 
            IProcessManager * This);
        
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfoCount )( 
            IProcessManager * This,
            /* [out] */ UINT *pctinfo);
        
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfo )( 
            IProcessManager * This,
            /* [in] */ UINT iTInfo,
            /* [in] */ LCID lcid,
            /* [out] */ ITypeInfo **ppTInfo);
        
        HRESULT ( STDMETHODCALLTYPE *GetIDsOfNames )( 
            IProcessManager * This,
            /* [in] */ REFIID riid,
            /* [size_is][in] */ LPOLESTR *rgszNames,
            /* [range][in] */ UINT cNames,
            /* [in] */ LCID lcid,
            /* [size_is][out] */ DISPID *rgDispId);
        
        /* [local] */ HRESULT ( STDMETHODCALLTYPE *Invoke )( 
            IProcessManager * This,
            /* [in] */ DISPID dispIdMember,
            /* [in] */ REFIID riid,
            /* [in] */ LCID lcid,
            /* [in] */ WORD wFlags,
            /* [out][in] */ DISPPARAMS *pDispParams,
            /* [out] */ VARIANT *pVarResult,
            /* [out] */ EXCEPINFO *pExcepInfo,
            /* [out] */ UINT *puArgErr);
        
        /* [helpstring][id] */ HRESULT ( STDMETHODCALLTYPE *RsCreateProcess )( 
            IProcessManager * This,
            /* [in] */ ULONG dwOwnerPid,
            /* [in] */ ULONG dwSessionId,
            /* [in] */ BSTR strMainPath,
            /* [in] */ BSTR strCmdPara,
            /* [in] */ BSTR strWorkPath,
            /* [in] */ ULONG dwType);
        
        END_INTERFACE
    } IProcessManagerVtbl;

    interface IProcessManager
    {
        CONST_VTBL struct IProcessManagerVtbl *lpVtbl;
    };

    

#ifdef COBJMACROS


#define IProcessManager_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define IProcessManager_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define IProcessManager_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#define IProcessManager_GetTypeInfoCount(This,pctinfo)	\
    ( (This)->lpVtbl -> GetTypeInfoCount(This,pctinfo) ) 

#define IProcessManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo)	\
    ( (This)->lpVtbl -> GetTypeInfo(This,iTInfo,lcid,ppTInfo) ) 

#define IProcessManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)	\
    ( (This)->lpVtbl -> GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) ) 

#define IProcessManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)	\
    ( (This)->lpVtbl -> Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) ) 


#define IProcessManager_RsCreateProcess(This,dwOwnerPid,dwSessionId,strMainPath,strCmdPara,strWorkPath,dwType)	\
    ( (This)->lpVtbl -> RsCreateProcess(This,dwOwnerPid,dwSessionId,strMainPath,strCmdPara,strWorkPath,dwType) ) 

#endif /* COBJMACROS */


#endif 	/* C style interface */




#endif 	/* __IProcessManager_INTERFACE_DEFINED__ */



#ifndef __runproxyLib_LIBRARY_DEFINED__
#define __runproxyLib_LIBRARY_DEFINED__

/* library runproxyLib */
/* [helpstring][version][uuid] */ 


EXTERN_C const IID LIBID_runproxyLib;

#ifndef ___IProcessManagerEvents_DISPINTERFACE_DEFINED__
#define ___IProcessManagerEvents_DISPINTERFACE_DEFINED__

/* dispinterface _IProcessManagerEvents */
/* [helpstring][uuid] */ 


EXTERN_C const IID DIID__IProcessManagerEvents;

#if defined(__cplusplus) && !defined(CINTERFACE)

    MIDL_INTERFACE("7DDF5E93-D367-43EA-881E-63AD5D448D9F")
    _IProcessManagerEvents : public IDispatch
    {
    };
    
#else 	/* C style interface */

    typedef struct _IProcessManagerEventsVtbl
    {
        BEGIN_INTERFACE
        
        HRESULT ( STDMETHODCALLTYPE *QueryInterface )( 
            _IProcessManagerEvents * This,
            /* [in] */ REFIID riid,
            /* [annotation][iid_is][out] */ 
            __RPC__deref_out  void **ppvObject);
        
        ULONG ( STDMETHODCALLTYPE *AddRef )( 
            _IProcessManagerEvents * This);
        
        ULONG ( STDMETHODCALLTYPE *Release )( 
            _IProcessManagerEvents * This);
        
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfoCount )( 
            _IProcessManagerEvents * This,
            /* [out] */ UINT *pctinfo);
        
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfo )( 
            _IProcessManagerEvents * This,
            /* [in] */ UINT iTInfo,
            /* [in] */ LCID lcid,
            /* [out] */ ITypeInfo **ppTInfo);
        
        HRESULT ( STDMETHODCALLTYPE *GetIDsOfNames )( 
            _IProcessManagerEvents * This,
            /* [in] */ REFIID riid,
            /* [size_is][in] */ LPOLESTR *rgszNames,
            /* [range][in] */ UINT cNames,
            /* [in] */ LCID lcid,
            /* [size_is][out] */ DISPID *rgDispId);
        
        /* [local] */ HRESULT ( STDMETHODCALLTYPE *Invoke )( 
            _IProcessManagerEvents * This,
            /* [in] */ DISPID dispIdMember,
            /* [in] */ REFIID riid,
            /* [in] */ LCID lcid,
            /* [in] */ WORD wFlags,
            /* [out][in] */ DISPPARAMS *pDispParams,
            /* [out] */ VARIANT *pVarResult,
            /* [out] */ EXCEPINFO *pExcepInfo,
            /* [out] */ UINT *puArgErr);
        
        END_INTERFACE
    } _IProcessManagerEventsVtbl;

    interface _IProcessManagerEvents
    {
        CONST_VTBL struct _IProcessManagerEventsVtbl *lpVtbl;
    };

    

#ifdef COBJMACROS


#define _IProcessManagerEvents_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define _IProcessManagerEvents_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define _IProcessManagerEvents_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#define _IProcessManagerEvents_GetTypeInfoCount(This,pctinfo)	\
    ( (This)->lpVtbl -> GetTypeInfoCount(This,pctinfo) ) 

#define _IProcessManagerEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo)	\
    ( (This)->lpVtbl -> GetTypeInfo(This,iTInfo,lcid,ppTInfo) ) 

#define _IProcessManagerEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)	\
    ( (This)->lpVtbl -> GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) ) 

#define _IProcessManagerEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)	\
    ( (This)->lpVtbl -> Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) ) 

#endif /* COBJMACROS */


#endif 	/* C style interface */


#endif 	/* ___IProcessManagerEvents_DISPINTERFACE_DEFINED__ */


EXTERN_C const CLSID CLSID_ProcessManager;

#ifdef __cplusplus

class DECLSPEC_UUID("339141C3-4B27-40EC-A41E-EB0714C1F7EA")
ProcessManager;
#endif
#endif /* __runproxyLib_LIBRARY_DEFINED__ */

/* Additional Prototypes for ALL interfaces */

unsigned long             __RPC_USER  BSTR_UserSize(     unsigned long *, unsigned long            , BSTR * ); 
unsigned char * __RPC_USER  BSTR_UserMarshal(  unsigned long *, unsigned char *, BSTR * ); 
unsigned char * __RPC_USER  BSTR_UserUnmarshal(unsigned long *, unsigned char *, BSTR * ); 
void                      __RPC_USER  BSTR_UserFree(     unsigned long *, BSTR * ); 

/* end of Additional Prototypes */

#ifdef __cplusplus
}
#endif

#endif


