#ifndef _COMPAT_MEMORY_H_
#define _COMPAT_MEMORY_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef  __cplusplus
extern "C" 
{
#endif


void*	    heap_malloc(size_t size);
void*	    heap_calloc(size_t n,size_t size);
void*	    heap_realloc(void *mem_address, size_t size);
void	    heap_free(void *ptr);
char*	    heap_strdup(const char *str);

void*		s_memset(void *dst,int val,size_t count);
wchar_t*	s_wmemset(wchar_t *dst, wchar_t c, size_t count);

void*		s_memchr(const void *s, int c, size_t n);
wchar_t *	s_wmemchr(const wchar_t *s, wchar_t c, size_t n);



int			s_memicmp(const void * first,const void * last,size_t count);
int			s_wmemcmp(const wchar_t *first, const wchar_t *last, size_t count);

void*		s_memcpy(void * dest,const void * src,size_t count);
wchar_t *	s_wmemcpy(wchar_t *dest, const wchar_t *src, size_t count);


#ifdef __cplusplus
}
#endif

#endif

