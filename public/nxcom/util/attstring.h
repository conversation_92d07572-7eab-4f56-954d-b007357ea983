#ifndef _tstring_include_h_
#define _tstring_include_h_

#include <string>
#include <tchar.h>
using namespace std;

namespace atsdk {

namespace extension
{

typedef TCHAR tchar;

typedef basic_string< tchar > tstring;


// Function name	: trim
// Description	    : Trm function
// Return type		: basic_string< E >
// Argument         : const E* s
template< class E > 
basic_string< E > trim( const E* s )
{
	if( s == NULL )
		return basic_string< E >();

	const E* i = s;
	while( ( *i != '\0' ) && ( *i == ' ' ) )
		i++;

	if( *i == '\0' )
		return basic_string< E >();

	const E* j = i;

	while( *( j + 1 ) )
		j++;

	while( ( j != i ) && ( *j == ' ' ) )
		j--;

	return basic_string< E >( i, j - i + 1 );
}

// Function name	: remove_non_lws
// Description	    : Removes non linear white spaces
// Return type		: basic_string< E >
// Argument         : const E* s
template< class E > 
basic_string< E > remove_non_lws( const E* s )
{
	basic_string< E > strResult = s;

	basic_string< E >::size_type i = basic_string< E >::npos;

	// Remove \r
	while( ( i = strResult.find( '\r' ) ) != basic_string< E >::npos )
		strResult.erase( i, 1 );

	// Remove \n
	while( ( i = strResult.find( '\n' ) ) != basic_string< E >::npos )
		strResult.erase( i, 1 );

	// Remove \r
	while( ( i = strResult.find( '\r' ) ) != basic_string< E >::npos )
		strResult.erase( i, 1 );

	return strResult;
}

template< class E > 
basic_string< E >& strupr(basic_string< E >& str)
{
	int idx = 0;
	while (idx != str.size())
	{
		if ((str[idx] <= 'z') && (str[idx] >= 'a'))
			str[idx] = (str[idx] + ('A' - 'a'));
		idx++;
	}
	return str;
}

template< class E > 
basic_string< E >& strlwr(basic_string< E >& str)
{
	int idx = 0;
	while (idx != str.size())
	{
		if ((str[idx] <= 'Z') && (str[idx] >= 'A'))
			str[idx] = (str[idx] - ('A' - 'a'));
		idx++;
	}
	return str;
}

} // Namespace extension

} //namespace atsdk

#endif // _tstring_include_h_