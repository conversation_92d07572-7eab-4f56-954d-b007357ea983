#ifndef _UTIL_COMUTIL_HPP_
#define _UTIL_COMUTIL_HPP_


#ifndef MIDL_INTERFACE
#define MIDL_INTERFACE(x)   struct DECLSPEC_UUID(x)
#endif


#ifndef MIDL_INTERFACE
#define MIDL_INTERFACE(x)   struct DECLSPEC_UUID(x)
#endif


#ifndef DECLSPEC_UUID
#ifdef _MSC_VER
#define DECLSPEC_UUID(x) __declspec(uuid(x))
#else
#define DECLSPEC_UUID(x)
#endif

#endif



#ifndef DECLSPEC_SELECTANY
#define DECLSPEC_SELECTANY

#ifdef _MSC_VER
#define DECLSPEC_SELECTANY  __declspec(selectany)
#else
#define DECLSPEC_SELECTANY
#endif

#endif


#ifndef _HRESULT_DEFINED
#define _HRESULT_DEFINED
typedef long HRESULT;
#endif


#ifndef S_SUCCESS								
#define S_SUCCESS								0
#endif	// !S_SUCCESS

#ifndef S_ERROR									
#define S_ERROR									1
#endif	// !S_ERROR

#ifndef S_FAILD									
#define S_FAILD									10
#endif	// !S_FAILD


#ifndef S_OK
#define S_OK									((HRESULT)0L)
#endif // !S_OK

#ifndef S_FALSE
#define S_FALSE									((HRESULT)1L)
#endif // !S_FALSE

#ifndef S_DELETE
#define S_DELETE								((HRESULT)10L)
#endif // !S_DELETE

#ifndef SUCCEEDED
#define SUCCEEDED(hr)							((HRESULT)(hr) >= 0)
#endif

#ifndef FAILED
#define FAILED(hr)								((HRESULT)(hr) < 0)
#endif

#ifndef PURE
#define PURE									= 0
#endif

#define std_method(method) 					    virtual HRESULT OS_STDCALL method
#define std_method_impl							HRESULT OS_STDCALL

#define std_method_(type,method)		        virtual type OS_STDCALL method
#define std_method_type_impl(type)				type OS_STDCALL   



                   
#ifdef _MSC_VER

#else
#define E_NOTIMPL					            1002
#define E_OUTOFMEMORY							1003
#define E_INVALIDARG							1004
#define E_NOINTERFACE							1005
#define E_FAIL									1009
#endif


#endif


