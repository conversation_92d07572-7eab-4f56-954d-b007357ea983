#ifndef _UTIL_TARGETBITS_HPP_
#define _UTIL_TARGETBITS_HPP_


#define 	OS_WINDOWS					0
#define		OS_WINBASE					1
#define		OS_WINCE					2

#define 	OS_POSIX					100
#define 	OS_LINUX					101
#define		OS_ANDROID					102
#define 	OS_DEBIAN					103
#define 	OS_UBUNTU					104
#define 	OS_CENTOS					105
#define 	OS_REDHAT					106
				

#define		OS_DARWIN					200
#define 	OS_MAC						201
#define		OS_IOS						202
#define 	OS_FREEBSD					203
#define		OS_NETBSD					204
#define		OS_OPENBSD					205
#define		OS_SOLARIS					206



#define		CPU_SYSTEM					300
#define		CPU_X86						301
#define		CPU_ARM						302
#define		CPU_MIPS					303
#define		CPU_ALPHA					304
#define		CPU_POWERPC					305

#define 	CC_MSVC						0
#define 	CC_GCC						1
#define		CC_CLANG					2
#define		CC_LLVM						3
#define		CC_INTER					4
#define		CC_NDK						5


#define 	ARCH_32BIT					32
#define 	ARCH_64BIT					64

#define		TARGET_X86					10
#define		TARGET_X64					11

#define		TARGET_MIPS_X86				20
#define		TARGET_MIPS_X64				21

#define		TARGET_ARM_X86				30
#define		TARGET_ARM_X64				31




#if defined(WIN64) || defined(_WIN64)
	#define 	TARGET_OS		OS_WINDOWS
	#define 	OS_COMPILER 	CC_MSVC
	#define 	TARGET_TYPE		OS_WINBASE
#elif defined(WIN32)|| defined(_WIN32)
	#define 	TARGET_OS		OS_WINDOWS
	#define 	OS_COMPILER 	CC_MSVC
	#define 	TARGET_TYPE		OS_WINBASE
#elif defined(ANDROID) || defined(__ANDROID__)
	#define		TARGET_OS		OS_POSIX
	#define 	OS_COMPILER		CC_CLANG
	#define 	TARGET_TYPE		OS_ANDROID
#elif defined(linux) || defined(__linux) || defined(__linux__)
	#define		TARGET_OS		OS_POSIX
	#define 	OS_COMPILER		CC_GCC	
	#define 	TARGET_TYPE		OS_LINUX
#elif defined(__APPLE__) && (defined(__GNUC__) || defined(__xlC__) || defined(__xlc__))
	#include <TargetConditionals.h>
	#if defined(TARGET_OS_MAC) && TARGET_OS_MAC
		#define TARGET_OS		OS_DARWIN
		#define OS_COMPILER		CC_CLANG
		#define TARGET_TYPE		OS_MAC
	#elif defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
		#define TARGET_OS		OS_DARWIN
		#define OS_COMPILER		CC_CLANG
		#define TARGET_TYPE		OS_IOS
	#endif
#elif defined(__FreeBSD__) || defined(__FreeBSD_kernel__)
	#define 	TARGET_OS		OS_DARWIN
	#define 	OS_COMPILER		CC_GCC	
	#define		TARGET_TYPE		OS_FREEBSD
#elif defined(__NetBSD__)
	#define 	TARGET_OS		OS_DARWIN
	#define 	OS_COMPILER		CC_GCC	
	#define		TARGET_TYPE		OS_NETBSD
#elif defined(__OpenBSD__)
	#define 	TARGET_OS		OS_DARWIN
	#define 	OS_COMPILER		CC_GCC	
	#define		TARGET_TYPE		OS_OPENBSD
#elif defined(sun) || defined(__sun) || defined(__sun__)
	#define		TARGET_OS		OS_DARWIN
	#define 	OS_COMPILER		CC_GCC	
	#define		TARGET_TYPE		OS_SOLARIS
#else
	#error "Unsupported operating system platform!"
#endif


//i686 mips armv5te armv7-a
#define 	ARCH_32BIT					32
//AArch64 x86_64 mips64 
#define 	ARCH_64BIT					64


#if (defined(i386) || defined(_i386) || defined(__i386) || defined(__i386__)) || (defined(_WIN32)) || defined(_M_IX86)
	#define		TARGET_BITS				ARCH_32BIT
#elif defined(_WIN64) || defined(__x86_64__) || defined(_M_X64)
	#define		TARGET_BITS				ARCH_64BIT
#endif

#if !defined(TARGET_BITS)
	#define		TARGET_BITS				ARCH_64BIT
#endif


#endif
