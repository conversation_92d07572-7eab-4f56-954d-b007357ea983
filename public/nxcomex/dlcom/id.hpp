#ifndef _ID_HPP_
#define _ID_HPP_

#define _DEFINE_IID(iface, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) EXTERN_C const IID iface;

#define _DEFINE_GUID(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) EXTERN_C const GUID name;

#define _uuidof(iface)	(IID_##iface)

#define _DEFINE_IID_IMPL(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) \
        const IID name = {l,w1,w2,{b1,b2,b3,b4,b5,b6,b7,b8}};

#define _DEFINE_GUID_IMPL(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) \
        const GUID name = {l,w1,w2,{b1,b2,b3,b4,b5,b6,b7,b8}};



#endif

