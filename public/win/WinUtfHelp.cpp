#include "WinUtfHelp.h"
#include <string.h>
#include <windows.h>

namespace winHelper
{

std::wstring XToW(const char* lpszStr, unsigned int dwCodePage)
{
	if(!lpszStr)return std::wstring();
	unsigned int theLength = strlen(lpszStr);
	if(0 == theLength)
		return std::wstring();

	int nWideChar = MultiByteToWideChar(dwCodePage, 0, lpszStr, theLength, NULL, 0);
	if(0==nWideChar)
		return std::wstring();

	wchar_t* lpszBuffer = new wchar_t[nWideChar+1];
	memset(lpszBuffer, 0, sizeof(wchar_t) * (nWideChar + 1));
	MultiByteToWideChar(dwCodePage, 0, lpszStr, theLength, lpszBuffer, nWideChar+1);	
	std::wstring str = lpszBuffer;
	delete[] lpszBuffer;

	return str;
}

std::string WToX(const wchar_t* lpszStr, unsigned int dwCodePage)
{
	if(!lpszStr)return std::string();
	UINT theLength = wcslen(lpszStr);
	if(0 == theLength)
		return std::string();

	int nMultiChar = WideCharToMultiByte(dwCodePage, 0, lpszStr, theLength, NULL, 0, NULL, NULL);
	if(0 == nMultiChar)
		return std::string();

	char* lpszBuffer = new char[nMultiChar+1];
	ZeroMemory(lpszBuffer, sizeof(char)*(nMultiChar+1));
	WideCharToMultiByte(dwCodePage, 0, lpszStr, theLength, lpszBuffer, nMultiChar+1, NULL, NULL);
	std::string str = lpszBuffer;
	delete[] lpszBuffer;

	return str;
}

};

