#ifndef	_UTIL_ASSERT_HPP_
#define _UTIL_ASSERT_HPP_

#include <compat/log.h>


#define rs_assert(p,v)			{ if((p))	return v;}
#define rc_assert(p,v)			{ if(!(p))	return v;}
#define c_assert(p)				{ if(!(p))	return;	}
#define rc_assertp(p, v)		{ if((p))	return v;}
#define rc_assert_continue(p)	{ if(!(p)) 	continue; }
#define rc_assert_break(p)		{ if(!(p)) 	break; }


#define rc_assert_log(p,v,x)	{ if(!(p)){ loge("%s",x);return v;} }	
#define c_assert_log(p)			{ if(!(p)){ loge(x);return ;} }

#endif 
