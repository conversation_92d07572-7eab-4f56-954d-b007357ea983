#pragma once
#include <string>
#include <atomic>
#include <mutex>
#include "common_types.h"
#include "utilset/blocking_queue.hpp"
#if defined(_WIN32)
    #include "windows/usbmanager.h"
#else
    #include "linux/usb_hotplug_monitor.h"
#endif

class USBProtector
{
public:
    USBProtector(BlockingQueue<string>& report_msg_queue);
    ~USBProtector();

    void HandleAgentProxyMsg(const string &tag, const string &msg);
    void Start();
    void Stop();

private:
    void EnableAllUSBStorage();
    void DisableAllUSBStorage();
    template<typename T>
    void EnableUSBStorage(const T &device);
    template<typename T>
    void DisableUSBStorage(const T &device);

    std::atomic<bool> run_flag_;
    USBProtectorPolicy curr_policy_;
    std::mutex curr_policy_mtx_;
    BlockingQueue<string>& report_msg_queue_;

#if defined(_WIN32)
    USBManager usb_manager_;
#else
    USBHotplugMonitor usb_manager_;
#endif
};
