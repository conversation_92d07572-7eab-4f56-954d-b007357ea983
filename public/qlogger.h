
/*
  使用说明
  1 .使用时候包含public/qlogger.h头文件；
	win链接：public/qlog_x64_xxx.lib(分为mt,md,mtd,mdd4个版本）
	linux链接：libqlog.a
  2. 代码使用的话，提供简易方式：直接使用QLogXXXX的几个相关宏即可，这里第一个参数默认nullpter，含义是使用默认配置。如果想自己定义配置，直接使用
				bool NewLogger(const qlog_char* name, const qlog_char* path);
				void Log(const qlog_char* name, int level, const qlog_char* fmt, ...);
  3. name==null为默认配置，path==null为默认路径
  4.默认配置是（既name==nullptr)，当前进程同目录生成 进程名.log的文件。默认日式输出级别为Info ,默认log文件缓存大小100M 一个备份文件
  5.可以修改默认配置,进程同目录下创建qlog.ini 文件，添加如下格式行即可：
            ${all}=level:trace maxFileSize:100 maxBackupIndex:1   // ${all}代码所有的日志，
			logname= level:trace maxFileSize:100 maxBackupIndex:1
			log_dbglogger为写代码时候的日志name,
			level取值范围为：trace debug info warn error fatal
			maxFileSize单位为M,日志文件大小
			maxBackupIndex 备份的文件数量，默认（最小）为1
  5.1 上述文件的应用顺序是从上倒下应用，最后的设置覆盖之前的
  6. *A版本是anscii编码，*W版本是unicode版面, *Utf8版本是utf8编码
  7. linux不支持*W版本函数
*/


#define LOG_LEVEL_ALL		100
#define LOG_LEVEL_TRACE		100
#define LOG_LEVEL_DEBUG		200
#define LOG_LEVEL_INFO		300	//默认级别
#define LOG_LEVEL_WARN		400
#define LOG_LEVEL_ERROR		500
#define LOG_LEVEL_FATAL		600
#define LOG_LEVEL_OFF		1000

#if defined(_WIN32) || defined(_WIN64)
#define qlog_char wchar_t
#else
#define qlog_char char
#endif

#if !defined(_USE_QLOG) && defined(_USE_NXCOM)
#error need defined _USE_QLOG in project
#endif

#ifdef __cplusplus
extern "C"
{
#endif
	void QInitLog(); // main exec call
	void QUnInitLog();

// name == null 默认logger，具体文件路径由exec里面决定

	int QNewLogger(const qlog_char* name, const qlog_char* path);
	int QNewLoggerA(const char* name, const char* path);
	int QNewLoggerW(const wchar_t* name, const wchar_t* path);
	int QNewLoggerUtf8(const char* name, const char* path);

	void QLog(const qlog_char* name, int level, const qlog_char* fmt, ...);
	void QLogA(const char* name, int level, const char* fmt, ...);
	void QLogW(const wchar_t* name, int level, const wchar_t* fmt, ...);
	void QLogUtf8(const char* name, int level, const char* fmt, ...);
#ifdef __cplusplus
};
#endif

#if defined(_WIN32) || defined(_WIN64)
#define QLogTrace(name, fmt, ...)QLog(name, LOG_LEVEL_TRACE, fmt, __VA_ARGS__);
#define QLogDebug(name, fmt, ...)QLog(name, LOG_LEVEL_DEBUG, fmt, __VA_ARGS__);
#define QLogInfo(name, fmt, ...) QLog(name, LOG_LEVEL_INFO, fmt, __VA_ARGS__);
#define QLogWarn(name, fmt, ...) QLog(name, LOG_LEVEL_WARN, fmt, __VA_ARGS__);
#define QLogError(name, fmt, ...)QLog(name, LOG_LEVEL_ERROR, fmt, __VA_ARGS__);
#define QLogFatal(name, fmt, ...)QLog(name, LOG_LEVEL_FATAL, fmt, __VA_ARGS__);

#define QLogTraceA(name, fmt, ...)QLogA(name, LOG_LEVEL_TRACE, fmt, __VA_ARGS__);
#define QLogDebugA(name, fmt, ...)QLogA(name, LOG_LEVEL_DEBUG, fmt, __VA_ARGS__);
#define QLogInfoA(name, fmt, ...) QLogA(name, LOG_LEVEL_INFO, fmt, __VA_ARGS__);
#define QLogWarnA(name, fmt, ...) QLogA(name, LOG_LEVEL_WARN, fmt, __VA_ARGS__);
#define QLogErrorA(name, fmt, ...)QLogA(name, LOG_LEVEL_ERROR, fmt, __VA_ARGS__);
#define QLogFatalA(name, fmt, ...)QLogA(name, LOG_LEVEL_FATAL, fmt, __VA_ARGS__);

#define QLogTraceW(name, fmt, ...)QLogW(name, LOG_LEVEL_TRACE, fmt, __VA_ARGS__);
#define QLogDebugW(name, fmt, ...)QLogW(name, LOG_LEVEL_DEBUG, fmt, __VA_ARGS__);
#define QLogInfoW(name, fmt, ...) QLogW(name, LOG_LEVEL_INFO, fmt, __VA_ARGS__);
#define QLogWarnW(name, fmt, ...) QLogW(name, LOG_LEVEL_WARN, fmt, __VA_ARGS__);
#define QLogErrorW(name, fmt, ...)QLogW(name, LOG_LEVEL_ERROR, fmt, __VA_ARGS__);
#define QLogFatalW(name, fmt, ...)QLogW(name, LOG_LEVEL_FATAL, fmt, __VA_ARGS__);

#define QLogTraceUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_TRACE, fmt, __VA_ARGS__);
#define QLogDebugUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_DEBUG, fmt, __VA_ARGS__);
#define QLogInfoUtf8(name, fmt, ...) QLogUtf8(name, LOG_LEVEL_INFO, fmt, __VA_ARGS__);
#define QLogWarnUtf8(name, fmt, ...) QLogUtf8(name, LOG_LEVEL_WARN, fmt, __VA_ARGS__);
#define QLogErrorUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_ERROR, fmt, __VA_ARGS__);
#define QLogFatalUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_FATAL, fmt, __VA_ARGS__);
#else
#define QLogTrace(name, fmt, ...)QLog(name, LOG_LEVEL_TRACE, fmt, ##__VA_ARGS__);
#define QLogDebug(name, fmt, ...)QLog(name, LOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__);
#define QLogInfo(name, fmt, ...) QLog(name, LOG_LEVEL_INFO, fmt, ##__VA_ARGS__);
#define QLogWarn(name, fmt, ...) QLog(name, LOG_LEVEL_WARN, fmt, ##__VA_ARGS__);
#define QLogError(name, fmt, ...)QLog(name, LOG_LEVEL_ERROR, fmt, ##__VA_ARGS__);
#define QLogFatal(name, fmt, ...)QLog(name, LOG_LEVEL_FATAL, fmt, ##__VA_ARGS__);

#define QLogTraceA(name, fmt, ...)QLogA(name, LOG_LEVEL_TRACE, fmt, ##__VA_ARGS__);
#define QLogDebugA(name, fmt, ...)QLogA(name, LOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__);
#define QLogInfoA(name, fmt, ...) QLogA(name, LOG_LEVEL_INFO, fmt, ##__VA_ARGS__);
#define QLogWarnA(name, fmt, ...) QLogA(name, LOG_LEVEL_WARN, fmt, ##__VA_ARGS__);
#define QLogErrorA(name, fmt, ...)QLogA(name, LOG_LEVEL_ERROR, fmt, ##__VA_ARGS__);
#define QLogFatalA(name, fmt, ...)QLogA(name, LOG_LEVEL_FATAL, fmt, ##__VA_ARGS__);

#define QLogTraceW(name, fmt, ...)QLogW(name, LOG_LEVEL_TRACE, fmt, ##__VA_ARGS__);
#define QLogDebugW(name, fmt, ...)QLogW(name, LOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__);
#define QLogInfoW(name, fmt, ...) QLogW(name, LOG_LEVEL_INFO, fmt, ##__VA_ARGS__);
#define QLogWarnW(name, fmt, ...) QLogW(name, LOG_LEVEL_WARN, fmt, ##__VA_ARGS__);
#define QLogErrorW(name, fmt, ...)QLogW(name, LOG_LEVEL_ERROR, fmt, ##__VA_ARGS__);
#define QLogFatalW(name, fmt, ...)QLogW(name, LOG_LEVEL_FATAL, fmt, ##__VA_ARGS__);

#define QLogTraceUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_TRACE, fmt, ##__VA_ARGS__);
#define QLogDebugUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__);
#define QLogInfoUtf8(name, fmt, ...) QLogUtf8(name, LOG_LEVEL_INFO, fmt, ##__VA_ARGS__);
#define QLogWarnUtf8(name, fmt, ...) QLogUtf8(name, LOG_LEVEL_WARN, fmt, ##__VA_ARGS__);
#define QLogErrorUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_ERROR, fmt, ##__VA_ARGS__);
#define QLogFatalUtf8(name, fmt, ...)QLogUtf8(name, LOG_LEVEL_FATAL, fmt, ##__VA_ARGS__);
#endif
