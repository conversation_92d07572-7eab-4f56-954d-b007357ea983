#ifndef _UTIL_OSDEF_H_
#define _UTIL_OSDEF_H_

#include <stdbool.h>
#include <dlfcn.h>
#include <unistd.h>
#include <sys/socket.h>


//address 
#include <netinet/in.h>
//setsockopt opations
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <semaphore.h>
 
//#define int64		long long int
//#define uint64	unsigned long long int

#define	FMT_I64D	"%lld"

#if defined(ANDROID)
#define	FMT_I64U	"%lu"
#else
#define	FMT_I64U	"%llu"
#endif


#define CALLBACK
#define WINAPI
#define WINAPIV
#define APIENTRY    WINAPI
#define APIPRIVATE
#define PASCAL      pascal


#endif
