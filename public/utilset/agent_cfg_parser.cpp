#include "agent_cfg_parser.h"

void agent_cfg_parser::from_json(const nlohmann::json& j, Log& log)
{
    if (j.contains("level")) j.at("level").get_to(log.level);
    if (j.contains("count")) j.at("count").get_to(log.count);
    if (j.contains("path")) j.at("path").get_to(log.path);
    if (j.contains("size")) j.at("size").get_to(log.size);
    if (j.contains("expired")) j.at("expired").get_to(log.expired);
}

void agent_cfg_parser::from_json(const nlohmann::json& j, Server& server)
{
    if (j.contains("interval")) j.at("interval").get_to(server.interval);
    if (j.contains("addr")) j.at("addr").get_to(server.addr);
    if (j.contains("protocol")) j.at("protocol").get_to(server.protocol);
}

void agent_cfg_parser::from_json(const nlohmann::json& j, Proxy& proxy)
{
    if (j.contains("port")) j.at("port").get_to(proxy.port);
}

void agent_cfg_parser::from_json(const nlohmann::json& j, Plugins& plugins)
{
    if (j.contains("listen")) j.at("listen").get_to(plugins.listen);
}

void agent_cfg_parser::from_json(const nlohmann::json& j, Extends& extends) {
    if (j.contains("avl")) j.at("avl").get_to(extends.avl);
    if (j.contains("gproxy")) j.at("gproxy").get_to(extends.gproxy);
    if (j.contains("interact")) j.at("interact").get_to(extends.interact);
    if (j.contains("mladdr")) j.at("mladdr").get_to(extends.mladdr);
    if (j.contains("rasp")) j.at("rasp").get_to(extends.rasp);
    if (j.contains("staddr")) j.at("staddr").get_to(extends.staddr);
    if (j.contains("static")) j.at("static").get_to(extends.static_url);
    if (j.contains("update")) j.at("update").get_to(extends.update);
    if (j.contains("upload")) j.at("upload").get_to(extends.upload);
    if (j.contains("vul")) j.at("vul").get_to(extends.vul);
}

void agent_cfg_parser::from_json(const nlohmann::json& j, Config& config) {
    if (j.contains("guid")) j.at("guid").get_to(config.guid);
    if (j.contains("hostname")) j.at("hostname").get_to(config.hostname);
    if (j.contains("ip")) j.at("ip").get_to(config.ip);
    if (j.contains("daemon")) j.at("daemon").get_to(config.daemon);
    if (j.contains("installdir")) j.at("installdir").get_to(config.installdir);
    if (j.contains("tenant")) j.at("tenant").get_to(config.tenant);
    if (j.contains("uninstall")) j.at("uninstall").get_to(config.uninstall);
    if (j.contains("socketport")) j.at("socketport").get_to(config.socketport);
    if (j.contains("dispatcherport")) j.at("dispatcherport").get_to(config.dispatcherport);
    if (j.contains("collaborativeprojects")) j.at("collaborativeprojects").get_to(config.collaborativeprojects);
    if (j.contains("log")) j.at("log").get_to(config.log);
    if (j.contains("server")) j.at("server").get_to(config.server);
    if (j.contains("proxy")) j.at("proxy").get_to(config.proxy);
    if (j.contains("plugins")) j.at("plugins").get_to(config.plugins);
    if (j.contains("extends")) j.at("extends").get_to(config.extends);
}