#pragma once
#include "commproxybase.h"

struct IDrvInst : public ICommBase
{
	virtual bool StartDrv() = 0;
	virtual bool StopDrv() = 0;
	virtual bool InstallDrv() = 0;
	virtual bool UninstallDrv() = 0;
	virtual void PrintDrvPath() = 0;
};

_DEF_IID(IDrvInst, "{A2F1D875-C0BC-4389-90F4-64847123404A}", 0xa2f1d875, 0xc0bc, 0x4389, 0x90, 0xf4, 0x64, 0x84, 0x71, 0x23, 0x40, 0x4a);

// {608BAF0F-BCDC-4A15-A75D-6E66073179D3}
_DEF_GUID(CLSID_DrvInst,
	0x608baf0f, 0xbcdc, 0x4a15, 0xa7, 0x5d, 0x6e, 0x66, 0x7, 0x31, 0x79, 0xd3);
