#ifndef _UTIL_DEBUG_HPP_
#define _UTIL_DEBUG_HPP_

#include <util/core.hpp>


#ifndef	_TOSTRING
#define	_TOSTRING(x) #x 
#endif 

#define	STR(x) _TOSTRING(x)

#if (TARGET_OS == OS_WINDOWS)

#define _STR(x) #x

#define $TODO(x) __pragma(message(__FILE__ "(" STR(__LINE__) ") :TODO: "_STR(x) " :: " ))

#define debug_view(x) { OutputDebugStringA(x);}	
#define wdebug_view(x) { OutputDebugStringW(x); }

#elif (TARGET_OS == OS_POSIX)
#define debug_view(x)	printf("%s",x);
#define wdebug_view(x)	printf("%s",x);
#endif


#endif
