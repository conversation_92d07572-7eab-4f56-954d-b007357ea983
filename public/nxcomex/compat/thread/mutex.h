#ifndef _COMPAT_MUTEX_H_
#define _COMPAT_MUTEX_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

int		_mutex_init(_mutex_t* mutex);
int		_mutex_init_recursive(_mutex_t* mutex);
void	_mutex_destroy(_mutex_t* mutex);
void	_mutex_lock(_mutex_t* mutex);
int		_mutex_trylock(_mutex_t* mutex);
void	_mutex_unlock(_mutex_t* mutex);

#ifdef	__cplusplus
}
#endif

#endif

