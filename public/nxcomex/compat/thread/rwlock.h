#ifndef _COMPAT_RWLOCK_H_
#define _COMPAT_RWLOCK_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

int		_rwlock_init(_rwlock_t* rwlock);
void	_rwlock_destroy(_rwlock_t* rwlock);
void	_rwlock_rdlock(_rwlock_t* rwlock);
int		_rwlock_tryrdlock(_rwlock_t* rwlock);
void	_rwlock_rdunlock(_rwlock_t* rwlock);
void	_rwlock_wrlock(_rwlock_t* rwlock);
int		_rwlock_trywrlock(_rwlock_t* rwlock);
void	_rwlock_wrunlock(_rwlock_t* rwlock);

#ifdef	__cplusplus
}
#endif

#endif
