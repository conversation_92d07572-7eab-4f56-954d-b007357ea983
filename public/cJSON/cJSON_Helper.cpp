#pragma once
#include "cJSON_Helper.h"
#include <tchar.h>

static std::wstring XToW(const char* lpszStr, unsigned int dwCodePage)
{
	if (!lpszStr)return std::wstring();
	size_t theLength = strlen(lpszStr);
	if (0 == theLength)
		return std::wstring();

	size_t nWideChar = MultiByteToWideChar(dwCodePage, 0, lpszStr, theLength, NULL, 0);
	if (0 == nWideChar)
		return std::wstring();

	wchar_t* lpszBuffer = new wchar_t[nWideChar + 1];
	memset(lpszBuffer, 0, sizeof(wchar_t) * (nWideChar + 1));
	MultiByteToWideChar(dwCodePage, 0, lpszStr, theLength, lpszBuffer, nWideChar + 1);
	std::wstring str = lpszBuffer;
	delete[] lpszBuffer;

	return str;
}

unsigned int cJSON_GetItemNumber(cJSON* object, const char* itemName, unsigned int def /*= 0*/)
{
	if (!(object && itemName))
		return def;

	if (!strlen(itemName))
		return def;

	auto* item = cJSON_GetObjectItem(object, itemName);
	if (!item)
		return def;
	
	return (unsigned int)cJSON_GetNumberValue(item);
}

std::string  cJSON_GetItemString(cJSON* object, const char* itemName, char* def /*= nullptr*/)
{
	if (!(object && itemName))
		return def;

	if (!strlen(itemName))
		return def;

	auto* item = cJSON_GetObjectItem(object, itemName);
	if (!item)
		return def?std::string(def):std::string();

	return cJSON_GetStringValue(item);
}

std::wstring cJSON_GetItemWString(cJSON* object, const char* itemName, char* def)
{
	std::string s = cJSON_GetItemString(object, itemName, def);
	if (s.size())
		return XToW(s.c_str(), CP_UTF8);
	else
		return std::wstring();
}

cJSON* cJSON_ParseFile(LPCTSTR lpszFile)
{
	if (!lpszFile)
		return nullptr;

	FILE* f = nullptr;
	errno_t err = _tfopen_s(&f, lpszFile, _T("rb"));
	if (err || !f)
		return nullptr;

	std::string ret;

	while (!feof(f)) {

		char buf[1024] = { 0 };
		size_t readSize = fread(buf, 1, 1024, f);
		if (readSize != 1024) {
			if (ferror(f)) {
				fclose(f);
				return nullptr;
			}
		}
		ret.append(buf, readSize);
	}

	fclose(f);

	if (!ret.size())
		return nullptr;

	return cJSON_Parse(ret.c_str());
}

///////////////////////////////////////////

class AutoFreeCJSONImpl
{
public:
	AutoFreeCJSONImpl():m_pJson(nullptr)
	{
	}

	~AutoFreeCJSONImpl()
	{
		free();
	}

	void SetPtr(cJSON* ptr) 
	{
		free();
		m_pJson = ptr;
	}

	cJSON* GetPtr()
	{
		return m_pJson;
	}

private:

	void free()
	{
		if (m_pJson) {
			cJSON_Delete(m_pJson);
			m_pJson = nullptr;
		}
	}

	cJSON* m_pJson;
};


AutoFreeCJSON::AutoFreeCJSON() :m_spRoot(nullptr)
{
	m_spRoot = std::make_shared<AutoFreeCJSONImpl>();
}

AutoFreeCJSON::~AutoFreeCJSON()
{
	m_spRoot = nullptr;
}

void AutoFreeCJSON::operator=(cJSON* pJson)
{
	if(!m_spRoot)
		m_spRoot = std::make_shared<AutoFreeCJSONImpl>();

	m_spRoot->SetPtr(pJson);
}

void AutoFreeCJSON::operator=(AutoFreeCJSON& r)
{
	m_spRoot = r.m_spRoot;
}

cJSON* AutoFreeCJSON::GetPtr()
{
	if (!m_spRoot)
		return nullptr;
	return m_spRoot->GetPtr();
}

void AutoFreeCJSON::ClearPtr()
{
	m_spRoot = nullptr;
}