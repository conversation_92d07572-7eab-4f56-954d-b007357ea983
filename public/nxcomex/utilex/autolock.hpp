#ifndef _UTILEX_AUTOLOCK_HPP_
#define _UTILEX_AUTOLOCK_HPP_

#include <util/util.h>

#ifdef __cplusplus

class CAutoLock
{
public:
	void Lock()
	{
		_mutex_lock(&lock);
	}

	void UnLock()
	{
		_mutex_unlock(&lock);
	}

	CAutoLock()
	{
		_mutex_init(&lock);
	}

	virtual ~CAutoLock()
	{
		_mutex_destroy(&lock);
	}

private:
	_mutex_t lock;
};

//////////////////////////////////////////////////////////
//wait wait wait --no use
class CSpinLock
{
public:
	CSpinLock()
	{

	}
	virtual ~CSpinLock()
	{

	}
	void Lock()
	{
		this->Add();
		m_lock.Lock();
	}
	void Unlock()
	{
		this->Release();
		m_lock.UnLock();
	}

private:
	long Add()
	{
		return atomicadd((LONG*)(LPVOID)&this->m_Ref);
	}

	long Release()
	{
		return atomicdel((LONG*)(LPVOID)&this->m_Ref);
	}

private:
	CAutoLock	m_lock;
	LONG		m_Ref;
};
//////////////////////////////////////////////////////////

template <class T>
class CStackLockWrapper
{
public:
	CStackLockWrapper(T& _t) : t(_t)
	{
		t.Lock();
	}
	~CStackLockWrapper()
	{
		t.UnLock();
	}
	T &t;
};

template <class T>
class CStackLockWrapper2
{
public:
	CStackLockWrapper2(T* _t) : t(_t)
	{
		t->Lock();
	}
	~CStackLockWrapper2()
	{
		t->UnLock();
	}
	T *t;
};



template<class LockType>
class CSrvCriticalSectionAtuoLock
{
public:
	LockType * m_lock;

	CSrvCriticalSectionAtuoLock(LockType* lock) :m_lock(lock)
	{
		m_lock->Lock();
	}

	~CSrvCriticalSectionAtuoLock()
	{
		m_lock->Unlock();
	}
};

typedef CStackLockWrapper2<CAutoLock>			CStackAutoCSLock;
typedef CStackLockWrapper2<CSpinLock>			CStackSpinCSLock;


#define		SYNC_OBJ(x)							CStackAutoCSLock lock(x);
#define		SYNC_OBJ_T(x)						CStackSpinCSLock lock(x);

#endif

#endif
