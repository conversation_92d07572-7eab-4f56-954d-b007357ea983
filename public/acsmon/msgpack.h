#pragma once


#include "commproxybase.h"
#include "gendef.h"

struct IMsgPack : public ICommBase
{
	virtual void* PackReplyMsg(struct event_reply_message* msg, uint32_t* ret_len)=0;
	virtual int CopyRequestMsg(struct event_request_message* new_msg, struct event_request_message* old_msg)=0;
	virtual void* UnpackRequestMsg(struct event_request_message* msg, uint32_t* ret_len)=0;

};

_DEF_IID(IMsgPack, "{513D8D23-DC26-4327-AAE8-348CCCFF8630}", 0x513d8d23, 0xdc26, 0x4327, 0xaa, 0xe8, 0x34, 0x8c, 0xcc, 0xff, 0x86, 0x30);


class MsgPack : public CommProxyBase<MsgPack>,
				public IMsgPack
{
public:
	MsgPack(void);
	virtual ~MsgPack(void);

	STD_QI1(IMsgPack);

	virtual HRESULT OnAfterInit();
	virtual HRESULT OnBeforeUninit();
	virtual HRESULT OnAfterStart();
	virtual HRESULT OnBeforeStop();

public:
	void* PackReplyMsg(struct event_reply_message* msg, uint32_t* ret_len)  override;
	int CopyRequestMsg(struct event_request_message* new_msg, struct event_request_message* old_msg) override;
	void* UnpackRequestMsg(struct event_request_message* msg, uint32_t* ret_len) override;
};


// {99DC8D5D-0BBC-482B-8D7E-8C775A7880EE}
_DEF_GUID(CLSID_MsgPack,
	0x99dc8d5d, 0xbbc, 0x482b, 0x8d, 0x7e, 0x8c, 0x77, 0x5a, 0x78, 0x80, 0xee);
