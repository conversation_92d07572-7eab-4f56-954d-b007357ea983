#ifndef	_COMPAT_SOCKINET_H_
#define	_COMPAT_SOCKINET_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

#define VALID_HOSTNAME_LEN	255	/* RFC 1035 */
#define VALID_LABEL_LEN		63	/* RFC 1035 */

#define DONT_GRIPE		0
#define DO_GRIPE		1


//#ifndef INET_ADDRSTRLEN
//#define INET_ADDRSTRLEN   (sizeof("***************") - 1)
//#endif 
//
//
//#ifndef INET6_ADDRSTRLEN
//#define INET6_ADDRSTRLEN   (sizeof("ffff:ffff:ffff:ffff:ffff:ffff:***************") - 1)
//#endif 

int		_valid_hostaddr(const char *, int);
int		_valid_ipv4_hostaddr(const char *, int);
int		_valid_ipv6_hostaddr(const char *, int);
int		_is_ip(const char *ip);
int		_is_ipv4(const char *ip);
int		_is_ipv6(const char *ip);
int		_ipv4_addr_valid(const char *addr);
int		_inet_pton(int af, const char *src, void *dst);
char*	_inet_ntop(int af, const void *src, char *buf, size_t size);
unsigned int _inet_addr_v4(const char* ip);


#ifdef	__cplusplus
}
#endif

#endif


