#ifndef _ATGUIDCRC_H
#define _ATGUIDCRC_H

namespace atsdk {
	static unsigned long  std_crc32(const char * Buf, size_t BufSize)
	{
		static unsigned long crctable[] =
		{
			0x00000000,0x77073096,0xEE0E612C,0x990951BA,0x076DC419,0x706AF48F,0xE963A535,0x9E6495A3,
			0x0EDB8832,0x79DCB8A4,0xE0D5E91E,0x97D2D988,0x09B64C2B,0x7EB17CBD,0xE7B82D07,0x90BF1D91,
			0x1DB71064,0x6AB020F2,0xF3B97148,0x84BE41DE,0x1ADAD47D,0x6DDDE4EB,0xF4D4B551,0x83D385C7,
			0x136C9856,0x646BA8C0,0xFD62F97A,0x8A<PERSON>C9EC,0x14015C4F,0x63066CD9,0xFA0F3D63,0x8D080DF5,
			0x3B6E20C8,0x4C69105E,0xD56041E4,0xA2677172,0x3C03E4D1,0x4B04D447,0xD20D85FD,0xA50AB56B,
			0x35B5A8FA,0x42B2986C,0xDBBBC9D6,0xACBCF940,0x32D86CE3,0x45DF5C75,0xDCD60DCF,0xABD13D59,
			0x26D930AC,0x51DE003A,0xC8D75180,0xBFD06116,0x21B4F4B5,0x56B3C423,0xCFBA9599,0xB8BDA50F,
			0x2802B89E,0x5F058808,0xC60CD9B2,0xB10BE924,0x2F6F7C87,0x58684C11,0xC1611DAB,0xB6662D3D,
			0x76DC4190,0x01DB7106,0x98D220BC,0xEFD5102A,0x71B18589,0x06B6B51F,0x9FBFE4A5,0xE8B8D433,
			0x7807C9A2,0x0F00F934,0x9609A88E,0xE10E9818,0x7F6A0DBB,0x086D3D2D,0x91646C97,0xE6635C01,
			0x6B6B51F4,0x1C6C6162,0x856530D8,0xF262004E,0x6C0695ED,0x1B01A57B,0x8208F4C1,0xF50FC457,
			0x65B0D9C6,0x12B7E950,0x8BBEB8EA,0xFCB9887C,0x62DD1DDF,0x15DA2D49,0x8CD37CF3,0xFBD44C65,
			0x4DB26158,0x3AB551CE,0xA3BC0074,0xD4BB30E2,0x4ADFA541,0x3DD895D7,0xA4D1C46D,0xD3D6F4FB,
			0x4369E96A,0x346ED9FC,0xAD678846,0xDA60B8D0,0x44042D73,0x33031DE5,0xAA0A4C5F,0xDD0D7CC9,
			0x5005713C,0x270241AA,0xBE0B1010,0xC90C2086,0x5768B525,0x206F85B3,0xB966D409,0xCE61E49F,
			0x5EDEF90E,0x29D9C998,0xB0D09822,0xC7D7A8B4,0x59B33D17,0x2EB40D81,0xB7BD5C3B,0xC0BA6CAD,
			0xEDB88320,0x9ABFB3B6,0x03B6E20C,0x74B1D29A,0xEAD54739,0x9DD277AF,0x04DB2615,0x73DC1683,
			0xE3630B12,0x94643B84,0x0D6D6A3E,0x7A6A5AA8,0xE40ECF0B,0x9309FF9D,0x0A00AE27,0x7D079EB1,
			0xF00F9344,0x8708A3D2,0x1E01F268,0x6906C2FE,0xF762575D,0x806567CB,0x196C3671,0x6E6B06E7,
			0xFED41B76,0x89D32BE0,0x10DA7A5A,0x67DD4ACC,0xF9B9DF6F,0x8EBEEFF9,0x17B7BE43,0x60B08ED5,
			0xD6D6A3E8,0xA1D1937E,0x38D8C2C4,0x4FDFF252,0xD1BB67F1,0xA6BC5767,0x3FB506DD,0x48B2364B,
			0xD80D2BDA,0xAF0A1B4C,0x36034AF6,0x41047A60,0xDF60EFC3,0xA867DF55,0x316E8EEF,0x4669BE79,
			0xCB61B38C,0xBC66831A,0x256FD2A0,0x5268E236,0xCC0C7795,0xBB0B4703,0x220216B9,0x5505262F,
			0xC5BA3BBE,0xB2BD0B28,0x2BB45A92,0x5CB36A04,0xC2D7FFA7,0xB5D0CF31,0x2CD99E8B,0x5BDEAE1D,
			0x9B64C2B0,0xEC63F226,0x756AA39C,0x026D930A,0x9C0906A9,0xEB0E363F,0x72076785,0x05005713,
			0x95BF4A82,0xE2B87A14,0x7BB12BAE,0x0CB61B38,0x92D28E9B,0xE5D5BE0D,0x7CDCEFB7,0x0BDBDF21,
			0x86D3D2D4,0xF1D4E242,0x68DDB3F8,0x1FDA836E,0x81BE16CD,0xF6B9265B,0x6FB077E1,0x18B74777,
			0x88085AE6,0xFF0F6A70,0x66063BCA,0x11010B5C,0x8F659EFF,0xF862AE69,0x616BFFD3,0x166CCF45,
			0xA00AE278,0xD70DD2EE,0x4E048354,0x3903B3C2,0xA7672661,0xD06016F7,0x4969474D,0x3E6E77DB,
			0xAED16A4A,0xD9D65ADC,0x40DF0B66,0x37D83BF0,0xA9BCAE53,0xDEBB9EC5,0x47B2CF7F,0x30B5FFE9,
			0xBDBDF21C,0xCABAC28A,0x53B39330,0x24B4A3A6,0xBAD03605,0xCDD70693,0x54DE5729,0x23D967BF,
			0xB3667A2E,0xC4614AB8,0x5D681B02,0x2A6F2B94,0xB40BBE37,0xC30C8EA1,0x5A05DF1B,0x2D02EF8D,
		};

		unsigned long CurCRC = 0xFFFFFFFF;
		while (BufSize-->0)
		{
			unsigned char ch = *Buf ^ (unsigned char)CurCRC;
			CurCRC >>= 8;
			CurCRC ^= crctable[ch];
			++Buf;
		}

		return CurCRC;
	}

	static BYTE update_crc8(unsigned char CurCRC, const char * Buf, size_t BufSize)
	{
#if 1
		// 0xea
		static unsigned char crctable[] =
		{
			0x00,0xec,0x0d,0xe1,0x1a,0xf6,0x17,0xfb,0x34,0xd8,0x39,0xd5,0x2e,0xc2,0x23,0xcf,0x68,0x84,0x65,0x89,0x72,0x9e,0x7f,0x93,0x5c,0xb0,0x51,0xbd,0x46,0xaa,0x4b,0xa7,
			0xd0,0x3c,0xdd,0x31,0xca,0x26,0xc7,0x2b,0xe4,0x08,0xe9,0x05,0xfe,0x12,0xf3,0x1f,0xb8,0x54,0xb5,0x59,0xa2,0x4e,0xaf,0x43,0x8c,0x60,0x81,0x6d,0x96,0x7a,0x9b,0x77,
			0x75,0x99,0x78,0x94,0x6f,0x83,0x62,0x8e,0x41,0xad,0x4c,0xa0,0x5b,0xb7,0x56,0xba,0x1d,0xf1,0x10,0xfc,0x07,0xeb,0x0a,0xe6,0x29,0xc5,0x24,0xc8,0x33,0xdf,0x3e,0xd2,
			0xa5,0x49,0xa8,0x44,0xbf,0x53,0xb2,0x5e,0x91,0x7d,0x9c,0x70,0x8b,0x67,0x86,0x6a,0xcd,0x21,0xc0,0x2c,0xd7,0x3b,0xda,0x36,0xf9,0x15,0xf4,0x18,0xe3,0x0f,0xee,0x02,
			0xea,0x06,0xe7,0x0b,0xf0,0x1c,0xfd,0x11,0xde,0x32,0xd3,0x3f,0xc4,0x28,0xc9,0x25,0x82,0x6e,0x8f,0x63,0x98,0x74,0x95,0x79,0xb6,0x5a,0xbb,0x57,0xac,0x40,0xa1,0x4d,
			0x3a,0xd6,0x37,0xdb,0x20,0xcc,0x2d,0xc1,0x0e,0xe2,0x03,0xef,0x14,0xf8,0x19,0xf5,0x52,0xbe,0x5f,0xb3,0x48,0xa4,0x45,0xa9,0x66,0x8a,0x6b,0x87,0x7c,0x90,0x71,0x9d,
			0x9f,0x73,0x92,0x7e,0x85,0x69,0x88,0x64,0xab,0x47,0xa6,0x4a,0xb1,0x5d,0xbc,0x50,0xf7,0x1b,0xfa,0x16,0xed,0x01,0xe0,0x0c,0xc3,0x2f,0xce,0x22,0xd9,0x35,0xd4,0x38,
			0x4f,0xa3,0x42,0xae,0x55,0xb9,0x58,0xb4,0x7b,0x97,0x76,0x9a,0x61,0x8d,0x6c,0x80,0x27,0xcb,0x2a,0xc6,0x3d,0xd1,0x30,0xdc,0x13,0xff,0x1e,0xf2,0x09,0xe5,0x04,0xe8
		};
#else
		// 0x9c
		static unsigned char crctable[] =
		{
			0x00,0x72,0xe4,0x96,0xf1,0x83,0x15,0x67,0xdb,0xa9,0x3f,0x4d,0x2a,0x58,0xce,0xbc,0x8f,0xfd,0x6b,0x19,0x7e,0x0c,0x9a,0xe8,0x54,0x26,0xb0,0xc2,0xa5,0xd7,0x41,0x33,
			0x27,0x55,0xc3,0xb1,0xd6,0xa4,0x32,0x40,0xfc,0x8e,0x18,0x6a,0x0d,0x7f,0xe9,0x9b,0xa8,0xda,0x4c,0x3e,0x59,0x2b,0xbd,0xcf,0x73,0x01,0x97,0xe5,0x82,0xf0,0x66,0x14,
			0x4e,0x3c,0xaa,0xd8,0xbf,0xcd,0x5b,0x29,0x95,0xe7,0x71,0x03,0x64,0x16,0x80,0xf2,0xc1,0xb3,0x25,0x57,0x30,0x42,0xd4,0xa6,0x1a,0x68,0xfe,0x8c,0xeb,0x99,0x0f,0x7d,
			0x69,0x1b,0x8d,0xff,0x98,0xea,0x7c,0x0e,0xb2,0xc0,0x56,0x24,0x43,0x31,0xa7,0xd5,0xe6,0x94,0x02,0x70,0x17,0x65,0xf3,0x81,0x3d,0x4f,0xd9,0xab,0xcc,0xbe,0x28,0x5a,
			0x9c,0xee,0x78,0x0a,0x6d,0x1f,0x89,0xfb,0x47,0x35,0xa3,0xd1,0xb6,0xc4,0x52,0x20,0x13,0x61,0xf7,0x85,0xe2,0x90,0x06,0x74,0xc8,0xba,0x2c,0x5e,0x39,0x4b,0xdd,0xaf,
			0xbb,0xc9,0x5f,0x2d,0x4a,0x38,0xae,0xdc,0x60,0x12,0x84,0xf6,0x91,0xe3,0x75,0x07,0x34,0x46,0xd0,0xa2,0xc5,0xb7,0x21,0x53,0xef,0x9d,0x0b,0x79,0x1e,0x6c,0xfa,0x88,
			0xd2,0xa0,0x36,0x44,0x23,0x51,0xc7,0xb5,0x09,0x7b,0xed,0x9f,0xf8,0x8a,0x1c,0x6e,0x5d,0x2f,0xb9,0xcb,0xac,0xde,0x48,0x3a,0x86,0xf4,0x62,0x10,0x77,0x05,0x93,0xe1,
			0xf5,0x87,0x11,0x63,0x04,0x76,0xe0,0x92,0x2e,0x5c,0xca,0xb8,0xdf,0xad,0x3b,0x49,0x7a,0x08,0x9e,0xec,0x8b,0xf9,0x6f,0x1d,0xa1,0xd3,0x45,0x37,0x50,0x22,0xb4,0xc6
		};
#endif
		while(BufSize-->0)
		{
			unsigned char ch = *Buf ^ (unsigned char)CurCRC;
			CurCRC = crctable[ch];
			++Buf;
		}
		return CurCRC;
	}

	static BYTE std_crc8(const char * data, size_t lens)
	{
		return ~update_crc8(0xFF, data, (DWORD)lens);
	}
//////////////////////////////////////////////////////////////////////////

	static unsigned short update_crc16(unsigned short CurCRC, const char * Buf, DWORD BufSize)
	{
		static unsigned short crctable[] =
		{
			0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
			0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
			0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
			0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
			0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
			0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
			0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
			0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
			0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
			0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
			0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
			0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
			0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
			0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
			0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
			0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
			0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
			0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
			0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
			0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
			0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
			0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
			0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
			0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
			0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
			0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
			0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
			0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
			0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
			0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
			0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
			0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040,
		};
		while(BufSize-->0)
		{
			unsigned char ch = *Buf ^ (unsigned char)CurCRC;
			CurCRC >>= 8;
			CurCRC ^= crctable[ch];
			++Buf;
		}
		return CurCRC;
	}


	static unsigned short std_crc16(const char *data, size_t lens)
	{
		return ~update_crc16(0xFFFF, data, (DWORD)lens);
	}
//////////////////////////////////////////////////////////////////////////

	static unsigned long long update_crc64(unsigned long long CurCRC, const char * Buf, DWORD BufSize)
	{
		static unsigned long long crctable[] =
		{
			0x0000000000000000LL,0x42F0E1EBA9EA3693LL,0x85E1C3D753D46D26LL,0xC711223CFA3E5BB5LL,0x493366450E42ECDFLL,0x0BC387AEA7A8DA4CLL,0xCCD2A5925D9681F9LL,0x8E224479F47CB76ALL,
			0x9266CC8A1C85D9BELL,0xD0962D61B56FEF2DLL,0x17870F5D4F51B498LL,0x5577EEB6E6BB820BLL,0xDB55AACF12C73561LL,0x99A54B24BB2D03F2LL,0x5EB4691841135847LL,0x1C4488F3E8F96ED4LL,
			0x663D78FF90E185EFLL,0x24CD9914390BB37CLL,0xE3DCBB28C335E8C9LL,0xA12C5AC36ADFDE5ALL,0x2F0E1EBA9EA36930LL,0x6DFEFF5137495FA3LL,0xAAEFDD6DCD770416LL,0xE81F3C86649D3285LL,
			0xF45BB4758C645C51LL,0xB6AB559E258E6AC2LL,0x71BA77A2DFB03177LL,0x334A9649765A07E4LL,0xBD68D2308226B08ELL,0xFF9833DB2BCC861DLL,0x388911E7D1F2DDA8LL,0x7A79F00C7818EB3BLL,
			0xCC7AF1FF21C30BDELL,0x8E8A101488293D4DLL,0x499B3228721766F8LL,0x0B6BD3C3DBFD506BLL,0x854997BA2F81E701LL,0xC7B97651866BD192LL,0x00A8546D7C558A27LL,0x4258B586D5BFBCB4LL,
			0x5E1C3D753D46D260LL,0x1CECDC9E94ACE4F3LL,0xDBFDFEA26E92BF46LL,0x990D1F49C77889D5LL,0x172F5B3033043EBFLL,0x55DFBADB9AEE082CLL,0x92CE98E760D05399LL,0xD03E790CC93A650ALL,
			0xAA478900B1228E31LL,0xE8B768EB18C8B8A2LL,0x2FA64AD7E2F6E317LL,0x6D56AB3C4B1CD584LL,0xE374EF45BF6062EELL,0xA1840EAE168A547DLL,0x66952C92ECB40FC8LL,0x2465CD79455E395BLL,
			0x3821458AADA7578FLL,0x7AD1A461044D611CLL,0xBDC0865DFE733AA9LL,0xFF3067B657990C3ALL,0x711223CFA3E5BB50LL,0x33E2C2240A0F8DC3LL,0xF4F3E018F031D676LL,0xB60301F359DBE0E5LL,
			0xDA050215EA6C212FLL,0x98F5E3FE438617BCLL,0x5FE4C1C2B9B84C09LL,0x1D14202910527A9ALL,0x93366450E42ECDF0LL,0xD1C685BB4DC4FB63LL,0x16D7A787B7FAA0D6LL,0x5427466C1E109645LL,
			0x4863CE9FF6E9F891LL,0x0A932F745F03CE02LL,0xCD820D48A53D95B7LL,0x8F72ECA30CD7A324LL,0x0150A8DAF8AB144ELL,0x43A04931514122DDLL,0x84B16B0DAB7F7968LL,0xC6418AE602954FFBLL,
			0xBC387AEA7A8DA4C0LL,0xFEC89B01D3679253LL,0x39D9B93D2959C9E6LL,0x7B2958D680B3FF75LL,0xF50B1CAF74CF481FLL,0xB7FBFD44DD257E8CLL,0x70EADF78271B2539LL,0x321A3E938EF113AALL,
			0x2E5EB66066087D7ELL,0x6CAE578BCFE24BEDLL,0xABBF75B735DC1058LL,0xE94F945C9C3626CBLL,0x676DD025684A91A1LL,0x259D31CEC1A0A732LL,0xE28C13F23B9EFC87LL,0xA07CF2199274CA14LL,
			0x167FF3EACBAF2AF1LL,0x548F120162451C62LL,0x939E303D987B47D7LL,0xD16ED1D631917144LL,0x5F4C95AFC5EDC62ELL,0x1DBC74446C07F0BDLL,0xDAAD56789639AB08LL,0x985DB7933FD39D9BLL,
			0x84193F60D72AF34FLL,0xC6E9DE8B7EC0C5DCLL,0x01F8FCB784FE9E69LL,0x43081D5C2D14A8FALL,0xCD2A5925D9681F90LL,0x8FDAB8CE70822903LL,0x48CB9AF28ABC72B6LL,0x0A3B7B1923564425LL,
			0x70428B155B4EAF1ELL,0x32B26AFEF2A4998DLL,0xF5A348C2089AC238LL,0xB753A929A170F4ABLL,0x3971ED50550C43C1LL,0x7B810CBBFCE67552LL,0xBC902E8706D82EE7LL,0xFE60CF6CAF321874LL,
			0xE224479F47CB76A0LL,0xA0D4A674EE214033LL,0x67C58448141F1B86LL,0x253565A3BDF52D15LL,0xAB1721DA49899A7FLL,0xE9E7C031E063ACECLL,0x2EF6E20D1A5DF759LL,0x6C0603E6B3B7C1CALL,
			0xF6FAE5C07D3274CDLL,0xB40A042BD4D8425ELL,0x731B26172EE619EBLL,0x31EBC7FC870C2F78LL,0xBFC9838573709812LL,0xFD39626EDA9AAE81LL,0x3A28405220A4F534LL,0x78D8A1B9894EC3A7LL,
			0x649C294A61B7AD73LL,0x266CC8A1C85D9BE0LL,0xE17DEA9D3263C055LL,0xA38D0B769B89F6C6LL,0x2DAF4F0F6FF541ACLL,0x6F5FAEE4C61F773FLL,0xA84E8CD83C212C8ALL,0xEABE6D3395CB1A19LL,
			0x90C79D3FEDD3F122LL,0xD2377CD44439C7B1LL,0x15265EE8BE079C04LL,0x57D6BF0317EDAA97LL,0xD9F4FB7AE3911DFDLL,0x9B041A914A7B2B6ELL,0x5C1538ADB04570DBLL,0x1EE5D94619AF4648LL,
			0x02A151B5F156289CLL,0x4051B05E58BC1E0FLL,0x87409262A28245BALL,0xC5B073890B687329LL,0x4B9237F0FF14C443LL,0x0962D61B56FEF2D0LL,0xCE73F427ACC0A965LL,0x8C8315CC052A9FF6LL,
			0x3A80143F5CF17F13LL,0x7870F5D4F51B4980LL,0xBF61D7E80F251235LL,0xFD913603A6CF24A6LL,0x73B3727A52B393CCLL,0x31439391FB59A55FLL,0xF652B1AD0167FEEALL,0xB4A25046A88DC879LL,
			0xA8E6D8B54074A6ADLL,0xEA16395EE99E903ELL,0x2D071B6213A0CB8BLL,0x6FF7FA89BA4AFD18LL,0xE1D5BEF04E364A72LL,0xA3255F1BE7DC7CE1LL,0x64347D271DE22754LL,0x26C49CCCB40811C7LL,
			0x5CBD6CC0CC10FAFCLL,0x1E4D8D2B65FACC6FLL,0xD95CAF179FC497DALL,0x9BAC4EFC362EA149LL,0x158E0A85C2521623LL,0x577EEB6E6BB820B0LL,0x906FC95291867B05LL,0xD29F28B9386C4D96LL,
			0xCEDBA04AD0952342LL,0x8C2B41A1797F15D1LL,0x4B3A639D83414E64LL,0x09CA82762AAB78F7LL,0x87E8C60FDED7CF9DLL,0xC51827E4773DF90ELL,0x020905D88D03A2BBLL,0x40F9E43324E99428LL,
			0x2CFFE7D5975E55E2LL,0x6E0F063E3EB46371LL,0xA91E2402C48A38C4LL,0xEBEEC5E96D600E57LL,0x65CC8190991CB93DLL,0x273C607B30F68FAELL,0xE02D4247CAC8D41BLL,0xA2DDA3AC6322E288LL,
			0xBE992B5F8BDB8C5CLL,0xFC69CAB42231BACFLL,0x3B78E888D80FE17ALL,0x7988096371E5D7E9LL,0xF7AA4D1A85996083LL,0xB55AACF12C735610LL,0x724B8ECDD64D0DA5LL,0x30BB6F267FA73B36LL,
			0x4AC29F2A07BFD00DLL,0x08327EC1AE55E69ELL,0xCF235CFD546BBD2BLL,0x8DD3BD16FD818BB8LL,0x03F1F96F09FD3CD2LL,0x41011884A0170A41LL,0x86103AB85A2951F4LL,0xC4E0DB53F3C36767LL,
			0xD8A453A01B3A09B3LL,0x9A54B24BB2D03F20LL,0x5D45907748EE6495LL,0x1FB5719CE1045206LL,0x919735E51578E56CLL,0xD367D40EBC92D3FFLL,0x1476F63246AC884ALL,0x568617D9EF46BED9LL,
			0xE085162AB69D5E3CLL,0xA275F7C11F7768AFLL,0x6564D5FDE549331ALL,0x279434164CA30589LL,0xA9B6706FB8DFB2E3LL,0xEB46918411358470LL,0x2C57B3B8EB0BDFC5LL,0x6EA7525342E1E956LL,
			0x72E3DAA0AA188782LL,0x30133B4B03F2B111LL,0xF7021977F9CCEAA4LL,0xB5F2F89C5026DC37LL,0x3BD0BCE5A45A6B5DLL,0x79205D0E0DB05DCELL,0xBE317F32F78E067BLL,0xFCC19ED95E6430E8LL,
			0x86B86ED5267CDBD3LL,0xC4488F3E8F96ED40LL,0x0359AD0275A8B6F5LL,0x41A94CE9DC428066LL,0xCF8B0890283E370CLL,0x8D7BE97B81D4019FLL,0x4A6ACB477BEA5A2ALL,0x089A2AACD2006CB9LL,
			0x14DEA25F3AF9026DLL,0x562E43B4931334FELL,0x913F6188692D6F4BLL,0xD3CF8063C0C759D8LL,0x5DEDC41A34BBEEB2LL,0x1F1D25F19D51D821LL,0xD80C07CD676F8394LL,0x9AFCE626CE85B507LL

		};
		while(BufSize-->0)
		{
			unsigned char ch = *Buf^(unsigned char)CurCRC;
			CurCRC >>= 8;
			CurCRC ^= crctable[ch];
			++Buf;
		}
		return CurCRC;
	}

	static unsigned long long std_crc64(const char *data, size_t lens)
	{
		return ~update_crc64(0xFFFFFFFFFFFFFFFFLL, data, (DWORD)lens);
	}

} //namespace atsdk

#endif	//_ATGUIDCRC_H