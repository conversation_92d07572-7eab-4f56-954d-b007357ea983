#ifndef _IO_THREADPOOL_H_
#define _IO_THREADPOOL_H_

#include <compat/funcb.h>
#include <compat/thread/sem.h>
#include <compat/thread/thread.h>

#ifdef	__cplusplus
extern "C"
{
#endif

typedef struct io_thread_s io_thread_t;

struct io_thread_s {
	void*				context;
	void*				parent_context;
	io_func_t			func;
	_thread_t			threadhandler;
	int					id;
};


typedef struct _threadpool_s _threadpool_t;

struct _threadpool_s {

	void*				context;
	unsigned long		count;

	io_func_t			func;
	io_thread_t*		threads;

	_sem_t			sem;
	_sem_t			exit;

};


int	init_threadpool(_threadpool_t*pool);
int	start_threadpool(_threadpool_t* pool);
int stop_threadpool(_threadpool_t* pool);
int	uinit_threadpool(_threadpool_t* pool);

#ifdef	__cplusplus
}
#endif

#endif


