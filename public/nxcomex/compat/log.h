#ifndef _COMPAT_LOG_H_
#define _COMPAT_LOG_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef __cplusplus
extern "C" {
#endif

#define log_error_tag		"error-tag:"
#define log_warning_tag		"warning-tag:"
#define log_info_tag		"info-tag:"
#define log_debug_tag		"debug-tag:"
#define LOG_verbose_tag		"verbost-tag:"

typedef enum sys_LogPriority {

		SYS_LOG_UNKNOWN = 0,
		SYS_LOG_DEFAULT, /* only for SetMinPriority() */
		SYS_LOG_VERBOSE,
		SYS_LOG_DEBUG,
		SYS_LOG_INFO,
		SYS_LOG_WARN,
		SYS_LOG_ERROR,
		SYS_LOG_FATAL,
		SYS_LOG_SILENT, /* only for SetMinPriority(); must be last */

} sys_LogPriority;

int log_print(int proi,
		const char *tag,
		const char *data,
		const char *time,
		const char *file,
		long line,
		const char* func,
		const char *fmt, ...);

#define loge(...)  \
	log_print(SYS_LOG_ERROR, log_error_tag, __DATE__, __TIME__, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)

#define logw(...)  \
	log_print(SYS_LOG_WARN, log_warning_tag, __DATE__, __TIME__, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)

#define logi(...)  \
	log_print(SYS_LOG_INFO, log_info_tag, __DATE__, __TIME__, __FILE__, __LINE__, __FUNCTION__,  __VA_ARGS__)

#define logd(...)  \
	log_print(SYS_LOG_DEBUG, log_debug_tag, __DATE__, __TIME__, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)

#define logv(...)  \
	log_print(SYS_LOG_VERBOSE, __DATE__, __TIME__, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)


#ifdef __cplusplus
}
#endif

#endif


