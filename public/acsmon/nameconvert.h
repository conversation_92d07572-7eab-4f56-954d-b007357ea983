#pragma once

#include <gendef.h>
#include "nameconv.h"
#include "commproxybase.h"

struct IUKNameConvert : public ICommBase
{
	virtual int ToUser(enum nconv_type type, const wchar_t* kname, wchar_t* uname, long length)=0;
	virtual int ToKernel(enum nconv_type type, const wchar_t* uname, wchar_t* kname, long length, bool_t redir)=0;
};

_DEF_IID(IUKNameConvert, "{A45471C6-AE96-4A50-9411-157E056CE4D6}", 0xa45471c6, 0xae96, 0x4a50, 0x94, 0x11, 0x15, 0x7e, 0x5, 0x6c, 0xe4, 0xd6);


class UKNameConvert : public CommProxyBase<UKNameConvert>,
						public IUKNameConvert
{
public:
	UKNameConvert(void);
	virtual ~UKNameConvert(void);

	STD_QI1(IUKNameConvert);

	virtual HRESULT OnAfterInit();
	virtual HRESULT OnBeforeUninit();
	virtual HRESULT OnAfterStart();
	virtual HRESULT OnBeforeStop();
public:
	int ToUser(enum nconv_type type, const wchar_t* kname, wchar_t* uname, long length) override;
	int ToKernel(enum nconv_type type, const wchar_t* uname, wchar_t* kname, long length, bool_t redir) override;

};


// {96BD857B-5FB7-4391-BDBD-C3C43E4FDC59}
_DEF_GUID(CLSID_UKNameConvert,
	0x96bd857b, 0x5fb7, 0x4391, 0xbd, 0xbd, 0xc3, 0xc4, 0x3e, 0x4f, 0xdc, 0x59);
