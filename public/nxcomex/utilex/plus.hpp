#ifndef _PLUS_H_
#define _PLUS_H_


#include <iostream>
using namespace std;
#include <string>
#include <list>
#include <map>
#include <vector>
#include <set>
#include <string.h>
#include <queue>


#if (TARGET_OS == OS_WINDOWS)
	typedef std::wstring	basic_tstring;
#elif (TARGET_OS == OS_POSIX)
	typedef std::string		basic_tstring;
#elif (TARGET_OS == OS_DARWIN)
	typedef std::string		basic_tstring;
#endif

#endif

