
#pragma once
#include <Psapi.h>
#include <util\utility_ex.h>
#pragma comment(lib, "Psapi.lib")

class CNTApi
{
public:
	typedef LONG (__stdcall * NTQUERYSYSTEMINFORMATION )( 
		DWORD SystemInformationClass,
		PVOID SystemInformation,
		ULONG SystemInformationLength,
		PULONG ReturnLength );

	enum { SystemHandleInformation = 0x10, };
#define STATUS_INFO_LENGTH_MISMATCH 0xC0000004

	typedef struct _SYSTEM_HANDLE_INFORMATION { // Information Class 16
		ULONG ProcessId;
		<PERSON>HAR ObjectTypeNumber;
		UCHAR Flags; // 0x01 = PROTECT_FROM_CLOSE, 0x02 = INHERIT
		USHORT Handle;
		PVOID Object;
		ACCESS_MASK GrantedAccess;
	} SYSTEM_HANDLE_INFORMATION, *PSYSTEM_HANDLE_INFORMATION;

	typedef struct _SYSTEM_HANDLE_INFORMATION_BLOCK
	{
		DWORD dwHandles;
		SYSTEM_HANDLE_INFORMATION stHandles[0];
	} SYSTEM_HANDLE_INFORMATION_BLOCK, *PSYSTEM_HANDLE_INFORMATION_BLOCK;

	static bool CloseRemoteHandle( DWORD dwPID, USHORT hHandle )
	{
		SAF_HANDLE hProc( OpenProcess( PROCESS_ALL_ACCESS, FALSE, dwPID ) );
		RASSERT( hProc, FALSE );
		SAF_HANDLE hLocalHandle;
		RASSERT( DuplicateHandle(hProc, 
			(HANDLE)hHandle, 
			GetCurrentProcess(),
			&hLocalHandle.m_p, 
			GENERIC_READ, 
			FALSE,
			DUPLICATE_CLOSE_SOURCE ), FALSE );
		return TRUE;
	}

	static bool GetRemoteFileNameByHandle( DWORD dwPID, USHORT hHandle, PWCHAR lpFileName, DWORD cbSize )
	{
		SAF_HANDLE hProc( OpenProcess( PROCESS_ALL_ACCESS, FALSE, dwPID ) );
		RASSERT( hProc, FALSE );
		SAF_HANDLE hLocalHandle;
		RASSERT( DuplicateHandle( hProc, (HANDLE)hHandle, GetCurrentProcess(), &hLocalHandle.m_p, 0, FALSE, DUPLICATE_SAME_ACCESS ), FALSE );
		SAF_HANDLE hMap = CreateFileMapping( hLocalHandle, NULL, PAGE_READONLY, 0, 1, NULL );
		RASSERT( hMap, FALSE );
		SAF_VIEW pView( MapViewOfFile( hMap, FILE_MAP_READ, 0, 0, 1 ) );
		RASSERT( pView, FALSE );
		DWORD dwTempFileNameSize = MAX_PATH*2 - 1;
		SAF_PTR(WCHAR) pTempFileName( new WCHAR[dwTempFileNameSize+1] );
		RASSERT( pTempFileName, FALSE );
		RASSERT( GetMappedFileName( GetCurrentProcess(), pView, pTempFileName, dwTempFileNameSize ), FALSE );
		// Translate path with device name to drive letters.
		WCHAR szTemp[MAX_PATH*2];
		szTemp[0] = L'\0';
		RASSERT( GetLogicalDriveStrings(MAX_PATH*2-1, szTemp), FALSE );
		PWCHAR lpDrive = szTemp;
		for(; lpDrive[0]; lpDrive += wcslen(lpDrive )+1 )
		{
			WCHAR szDeviceName[ MAX_PATH ];
			WCHAR szDosDevName[3] = L" :";
			szDosDevName[0] = lpDrive[0];
			DWORD ccRet = QueryDosDevice( szDosDevName, szDeviceName, MAX_PATH-1 );
			CONTINUE_IF( !ccRet );
			UINT uNameLen = wcslen(szDeviceName);
			CONTINUE_IF( wcsncmp( szDeviceName, pTempFileName.m_p, uNameLen ) );
			wnsprintf( lpFileName, cbSize-1, L"%s%s", szDosDevName, pTempFileName + uNameLen );
			return TRUE;
		}
		return FALSE;
	}

	static bool UnlockMalFile( PWCHAR pFilePath )
	{
		HMODULE hNTDLL( GetModuleHandleA( "NTDLL.DLL" ) );
		RASSERT( hNTDLL, FALSE );

		NTQUERYSYSTEMINFORMATION pfnNtQuerySystemInformation = 
			(NTQUERYSYSTEMINFORMATION)GetProcAddress( hNTDLL, "NtQuerySystemInformation" );

		RASSERT( pfnNtQuerySystemInformation, FALSE );

		NTSTATUS nr = STATUS_INFO_LENGTH_MISMATCH;
		DWORD dwHandles = 1;
		SAF_PTR(BYTE) pBuffer;
		for( ; nr == STATUS_INFO_LENGTH_MISMATCH; dwHandles += 0x1000 )
		{
			ULONG cbSize = sizeof(SYSTEM_HANDLE_INFORMATION_BLOCK) + dwHandles * sizeof(SYSTEM_HANDLE_INFORMATION);
			pBuffer = new BYTE[ cbSize ];
			nr = pfnNtQuerySystemInformation( SystemHandleInformation, pBuffer.m_p, cbSize, NULL );
		}

		RASSERT( SUCCEEDED(nr), FALSE );

		PSYSTEM_HANDLE_INFORMATION_BLOCK pHandleInfo = (PSYSTEM_HANDLE_INFORMATION_BLOCK)(pBuffer.m_p);
		for( DWORD i = 0; i < pHandleInfo->dwHandles; ++ i )
		{
			PSYSTEM_HANDLE_INFORMATION pHandle = pHandleInfo->stHandles + i;

			WCHAR szFileName[ MAX_PATH * 2 ];
			CONTINUE_IF( !GetRemoteFileNameByHandle( pHandle->ProcessId, pHandle->Handle, szFileName, MAX_PATH*2-1 ) );
			szFileName[MAX_PATH * 2-1] = 0;

			_wcslwr_s( szFileName,MAX_PATH * 2);
			CONTINUE_IF(  _wcsicmp(pFilePath,szFileName)!=0 );

			CloseRemoteHandle( pHandle->ProcessId, pHandle->Handle );
		}
		return TRUE;
	}
};