#ifndef _COMPAT_DEF_H_
#define _COMPAT_DEF_H_


#ifndef S_IOERROR								
#define S_IOERROR								-1
#endif	// !S_IOERROR

#ifndef S_SUCCESS								
#define S_SUCCESS								0
#endif	// !S_SUCCESS

#ifndef S_ERROR									
#define S_ERROR									1
#endif	// !S_ERROR

#ifndef S_FAILD									
#define S_FAILD									10
#endif	// !S_FAILD


#if  (TARGET_OS == OS_WINDOWS)

#define uu_fast(x) (x)
#define uu_slow(x) (x)

#elif (TARGET_OS == OS_POSIX)

#if defined __GNUC__ || defined __llvm__
#define uu_fast(x) __builtin_expect ((x), 1)
#define uu_slow(x) __builtin_expect ((x), 0)
#else
#define uu_fast(x) (x)
#define uu_slow(x) (x)
#endif

#elif (TARGET_OS == OS_DARWIN)

#endif



#define _container_of(ptr, type, member) \
    (ptr ? ((type*) (((char*) ptr) - offsetof(type, member))) : NULL)

#endif 


