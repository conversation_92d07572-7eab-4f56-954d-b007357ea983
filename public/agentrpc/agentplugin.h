#ifndef __PLUGINHELP_H_
#define __PLUGINHELP_H_

#include <string>
#include <thread>

#include "jsonrpc/jsonrpc.h"

using namespace std;

#define AGENT_PORT  55555
#define AGENT_IP    "127.0.0.1"


//Key
#define JSON_KEY_NAME			"name"
#define JSON_KEY_VERSION		"version"
#define J<PERSON><PERSON>_KEY_ADDR			"addr"
#define JSON_KEY_METHOD			"method"
#define JSON_KEY_ID				"id"
#define JSON_KEY_PARAMS			"params"


//method 
#define RPC_METHOD_HEALTH		"Plugin.Health"
#define RPC_METHOD_POLICY		"Plugin.Policy"
#define RPC_METHOD_TASK			"Plugin.Task"
#define RPC_METHOD_REPORT		"Agent.Report"
#define RPC_METHOD_REGISTER		"Agent.Register"
#define RPC_METHOD_UNREGISTER	"Agent.Unregister"

//res type
#define RES_TYPE_REGISTER   "Agent/Register"
#define RES_TYPE_ALIVE		"Agent/Alive"
#define RES_TYPE_UPDATE		"Agent/Update"
#define RES_TYPE_TASK		"Agent/Task"
#define RES_TYPE_POLICY     "Agent/Policy"
#define RES_TYPE_UPDATE		"Agent/Update"
#define RES_TYPE_ASSERT		"Agent/Assert"
#define RES_TYPE_METRICS	"Agent/Metrics"
#define RES_TYPE_LOG		"Agent/Log"
#define RES_TYPE_EVENT      "Agent/Event"



typedef int (*Rpcmsgcall)(string& strtag, string& strmsg);


class AgentPlugin
{
public:
	AgentPlugin();

	~AgentPlugin();

	int  Init(const string& pluginname, const string& strver, const string& agentip, unsigned short agentport, unsigned short listenport, Rpcmsgcall msgcallfun);

	int  Uninit();

	int  Report(const char* strrestype, const char* strdata, int len);

private:
	int rpcclient_init(const string& connip, unsigned short connport);

	int rpcsrv_init(unsigned short recvport);

	static void listen_thread(void* arg);

private:
	int  Register(const string& strname, const string& strver, const string& recvip, unsigned short recvport);

	int  Unregister(const string& strname, const string& strver);

	int  Senddata(const Json::Value& vdata);

	bool OnHealth(const Json::Value& msg, Json::Value& response);

	bool OnPolicy(const Json::Value& msg, Json::Value& response);

	bool OnTask(const Json::Value& msg, Json::Value& response);

private:
	string m_pluginname;

	string m_strver;

	string  m_agentip;

	unsigned short m_agentport;
	
	unsigned short m_listenport;

	Json::Rpc::TcpClient m_rpcClient;

	Json::Rpc::TcpServer m_rpcSrv;

	bool m_brun;

	std::thread listen_thread_;		

	Rpcmsgcall m_msgcallfun;
};


#endif /*__PLUGINHELP_H_*/
