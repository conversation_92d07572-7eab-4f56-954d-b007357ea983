#ifndef	_COMPAT_SOCK_H_
#define	_COMPAT_SOCK_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

	_sock_t _createsock(
		int af,
		int type,
		int protocol);

	int _closesock(_sock_t s);

	int _shutdown_sock(_sock_t s,int howrw);

	int SetError(_sock_t* s, int turn);
	int SetReuseAddr(_sock_t* s, int turn);
	int SetNoDelay(_sock_t* s, int turn);
	int SetBroadCast(_sock_t* s, int turn);
	int SetDontRoute(_sock_t* s, int turn);
	int SetKeepalive(_sock_t* s, int turn);
	int SetCork(_sock_t* s, int turn);
	int SetNoSigPipe(_sock_t* s, int turn);
	int SetNonblock(_sock_t* s, int turn);
	int SetDoLinger(_sock_t* s, int turn);
	int SetLinger(_sock_t* s, int sec);
	int SetSendTimeOut(_sock_t* s, int sec);
	int SetRecvTimeOut(_sock_t* s, int sec);
	int SetConTimeOut(_sock_t* s, int sec);
	int SetSendBufLen(_sock_t* s, int size);
	int SetRecvBufLen(_sock_t* s, int size);
	int SetSendLoWat(_sock_t* s, int size);
	int SetRecvLoWat(_sock_t* s, int size);

#if (TARGET_OS == OS_WINDOWS)
	int socketpair(int d, int type, int protocol, _sock_t sv[2]);
#endif

#ifdef	__cplusplus
}
#endif

#endif


