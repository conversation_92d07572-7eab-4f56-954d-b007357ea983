#include "wlthread.h"

namespace wlthread {

	bool wlmutex_init(wlmutex* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		InitializeCriticalSection(&p->sec);
#else
		pthread_mutex_init(&p->mutex, nullptr);
#endif
		return true;
	}
	bool wlmutex_uninit(wlmutex* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		DeleteCriticalSection(&p->sec);
#else
		pthread_mutex_destroy(&p->mutex);
#endif
		return true;
	}

	void wlmutex_lock(wlmutex* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		EnterCriticalSection(&p->sec);
#else
		pthread_mutex_lock(&p->mutex);
#endif
	}
	void wlmutex_unlock(wlmutex* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		LeaveCriticalSection(&p->sec);
#else
		pthread_mutex_unlock(&p->mutex);
#endif
	}

	void wlevent_init(wlevent* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		p->event = CreateEvent(nullptr, false, false, nullptr);
#else
		sem_init(&p->sem, 0, 0);
#endif
	}

	void wlevent_uninit(wlevent* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		CloseHandle(p->event);
#else
		sem_destroy(&p->sem);
#endif
	}

	void wlevent_set(wlevent* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		SetEvent(p->event);
#else
		sem_post(&p->sem);
#endif
	}

	unsigned int wlevent_wait(wlevent* p)
	{
#if defined(_WIN32) || defined(_WIN64)
		return WaitForSingleObject(p->event, INFINITE);
#else
		return sem_wait(&p->sem);
#endif
	}

};

