#include "usbmanager.h"
#include <setupapi.h>
// #include <devguid.h>
// #include <cfgmgr32.h>
#include <Dbt.h>
#include <PortableDevice.h>
#include <ndisguid.h>
#include "utilset/blocking_queue.hpp"

using std::wstring;

static BlockingQueue<wstring> kUSBNotifyQueue;

USBManager::USBManager()
{
    hwnd_ = nullptr;
    run_flag_ = true;
    mon_status_ = false;
}

USBManager::~USBManager()
{
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        break;

    case WM_DEVICECHANGE:
        if (wParam == DBT_DEVICEARRIVAL) {
            PDEV_BROADCAST_HDR pHdr = (PDEV_BROADCAST_HDR)lParam;
            if (pHdr->dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE) {
                PDEV_BROADCAST_DEVICEINTERFACE pDev = (PDEV_BROADCAST_DEVICEINTERFACE)pHdr;
                kUSBNotifyQueue.Push(pDev->dbcc_name);
            }
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }

    return 0;
}

static std::wstring ExtractDeviceInstanceFromInterfacePath(const std::wstring& interface_path) {
  // Device interface path format: \\?\USB#VID_0951&PID_1613#000AEB91BC945B8C060D00A5#{GUID}
  // Need to extract: USB\VID_0951&PID_1613\000AEB91BC945B8C060D00A5

  std::wstring path = interface_path;

  // Remove prefix
  if (path.find(L"\\\\?\\") == 0) {
    path = path.substr(4);
  }

  // Find the last # position (before GUID)
  size_t last_hash_pos = path.find_last_of(L'#');
  if (last_hash_pos != std::wstring::npos) {
    path = path.substr(0, last_hash_pos);
  }

  // Replace #
  std::replace(path.begin(), path.end(), L'#', L'\\');

  return path;
}

void USBManager::StartMonitor()
{
    if (mon_status_)
    {
        return;
    }
    
    // 注册窗口类
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = L"USBMonitor";

    RegisterClass(&wc);

    // 创建隐藏窗口
    hwnd_ = CreateWindow(L"USBMonitor", L"USB Monitor",
                         WS_OVERLAPPEDWINDOW, 0, 0, 0, 0, NULL, NULL,
                         GetModuleHandle(NULL), NULL);

    // 注册设备通知
    DEV_BROADCAST_DEVICEINTERFACE dbi = {};
    dbi.dbcc_size = sizeof(dbi);
    dbi.dbcc_devicetype = DBT_DEVTYP_DEVICEINTERFACE;
    dbi.dbcc_classguid = GUID_DEVINTERFACE_DISK;

    // 1. 注册 USB DISK
    HDEVNOTIFY hDevNotify = RegisterDeviceNotification(hwnd_, &dbi,
                                                       DEVICE_NOTIFY_WINDOW_HANDLE);

    // 2. 注册 CD-ROM
    dbi.dbcc_classguid = GUID_DEVINTERFACE_CDROM;
    HDEVNOTIFY hNotifyCdrom = RegisterDeviceNotification(hwnd_, &dbi,
                                                         DEVICE_NOTIFY_WINDOW_HANDLE);

    // 3. 注册 WPD（Windows Portable Device，比如手机）
    dbi.dbcc_classguid = GUID_DEVINTERFACE_WPD;
    HDEVNOTIFY hNotifyWpd = RegisterDeviceNotification(hwnd_, &dbi,
                                                       DEVICE_NOTIFY_WINDOW_HANDLE);

    // 4. 注册 Net
    dbi.dbcc_classguid = GUID_DEVINTERFACE_NET;
    HDEVNOTIFY hNotifyNet = RegisterDeviceNotification(hwnd_, &dbi,
                                                       DEVICE_NOTIFY_WINDOW_HANDLE);

    if (hDevNotify || hNotifyCdrom || hNotifyWpd)
    {
        mon_status_ = true;
    }
    
    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    if (hDevNotify)
    {
        UnregisterDeviceNotification(hDevNotify);
    }
    if (hNotifyCdrom)
    {
        UnregisterDeviceNotification(hNotifyCdrom);
    }
    if (hNotifyWpd)
    {
        UnregisterDeviceNotification(hNotifyWpd);
    }
    if (hNotifyNet)
    {
        UnregisterDeviceNotification(hNotifyNet);
    }

    if (hwnd_)
    {
        DestroyWindow(hwnd_);
    }
}

void USBManager::StopMonitor()
{
    PostMessage(hwnd_, WM_DESTROY, 0, 0);
    run_flag_ = false;
    mon_status_ = false;
}

std::wstring USBManager::GetNotifyUSBDevId()
{
    int ret = 0;
    wstring dev_id;
    static const wstring prefix = L"USB";
    do
    {
        auto dev_path = kUSBNotifyQueue.Pop();
        dev_id = ExtractDeviceInstanceFromInterfacePath(dev_path);
        ret = _wcsnicmp(dev_id.c_str(), prefix.c_str(), prefix.length());
    } while (ret != 0 && run_flag_);
    return dev_id;
}
