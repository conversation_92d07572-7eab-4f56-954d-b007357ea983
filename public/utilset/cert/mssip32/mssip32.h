//+-------------------------------------------------------------------------
//
//  Microsoft Windows
//
//  Copyright (C) Microsoft Corporation, 1996 - 1999
//
//  File:       mssip32.h
//
//  Contents:   Microsoft SIP Provider
//
//  Functions:  DllMain
//
//  History:    14-Feb-1997 p<PERSON><PERSON>   created
//
//--------------------------------------------------------------------------

#ifndef MSSIP32_H
#define MSSIP32_H

#define     MSSIP_ID_NONE                   0       // file only types
#define     MSSIP_ID_PE                     1
#define     MSSIP_ID_JAVA                   2
#define     MSSIP_ID_CAB                    3
#define     MSSIP_ID_FLAT                   4
#define     MSSIP_ID_CATALOG                5
#define     MSSIP_ID_CTL                    6
#define     MSSIP_ID_SS                     7

#define     MSSIP_V1ID_BEGIN                200
#define     MSSIP_V1ID_PE                   201
#define     MSSIP_V1ID_PE_EX                202
#define     MSSIP_V1ID_END                  299

#define     MSSIP_SUBJECT_FORM_FILE         1
#define     MSSIP_SUBJECT_FORM_FILEANDDISP  2

#define     MSSIP_CURRENT_VERSION           0x00000300

#define     OFFSETOF(t,f)                   ((DWORD)((DWORD_PTR)&((t*)0)->f))
#define     OBSOLETE_TEXT_W                 L"<<<Obsolete>>>"   // valid since 2/14/1997

#define     HASH_CACHE_LEN                  128

typedef void *HSPCDIGESTDATA;

typedef struct DIGEST_DATA
{
	union
	{
		HCRYPTHASH  hHash;
		BCRYPT_HASH_HANDLE hCNGHash;
	};
    DWORD       cbCache;
    BYTE        pbCache[HASH_CACHE_LEN];
    DWORD       dwAlgId;
    void        *pvSHA1orMD5Ctx;
	BCRYPT_ALG_HANDLE hCNGProv;

} DIGEST_DATA, *PDIGEST_DATA;

extern BOOL WINAPI DigestFileData(  IN HSPCDIGESTDATA hDigestData,
                                    IN const BYTE *pbData,
                                    IN DWORD cbData);

extern BOOL WINAPI mssip_CryptSIPVerifyIndirectData(	IN SIP_SUBJECTINFO      *pSubjectInfo,
													IN SIP_INDIRECT_DATA    *psData);
extern BOOL WINAPI mssip_CryptSIPCreateIndirectData( IN      SIP_SUBJECTINFO     *pSubjectInfo,
											 IN OUT  DWORD               *pdwDataLen,
											 OUT     SIP_INDIRECT_DATA   *psData);

extern void SipDestroyHash(DIGEST_DATA *psDigestData);
extern BYTE *SipGetHashValue(DIGEST_DATA *psDigestData, DWORD *pcbHash);
extern BOOL SipHashData(DIGEST_DATA *psDigestData, BYTE *pbData, DWORD cbData);
extern BOOL SipCreateHash(HCRYPTPROV hProv, DIGEST_DATA *psDigestData);

extern void CryptSIPGetRegWorkingFlags(DWORD *pdwState);
extern HCRYPTPROV mssip_CryptGetDefaultCryptProv(DWORD dw);

extern 
BOOL mssip_ImageGetCertificateData(
								   __in HANDLE FileHandle,
								   __in DWORD CertificateIndex,
								   __out LPWIN_CERTIFICATE Certificate,
								   __inout PDWORD RequiredLength
								   );
extern BOOL mssip_EnsureCNGAPIs();

extern NTSTATUS
(WINAPI
 *fnBCryptCreateHash)(
 __inout                         BCRYPT_ALG_HANDLE   hAlgorithm,
 __out                           BCRYPT_HASH_HANDLE  *phHash,
 __out_bcount_full(cbHashObject) PUCHAR   pbHashObject,
 __in                            ULONG   cbHashObject,
 __in_bcount_opt(cbSecret)       PUCHAR   pbSecret,   // optional
 __in                            ULONG   cbSecret,   // optional
 __in                            ULONG   dwFlags);


extern NTSTATUS
(WINAPI
 *fnBCryptHashData)(
 __inout                 BCRYPT_HASH_HANDLE  hHash,
 __in_bcount(cbInput)    PUCHAR   pbInput,
 __in                    ULONG   cbInput,
 __in                    ULONG   dwFlags);


extern NTSTATUS
(WINAPI
 *fnBCryptFinishHash)(
 __inout                     BCRYPT_HASH_HANDLE hHash,
 __out_bcount_full(cbOutput) PUCHAR   pbOutput,
 __in                        ULONG   cbOutput,
 __in                        ULONG   dwFlags);

extern NTSTATUS
(WINAPI
 *fnBCryptOpenAlgorithmProvider)(
 __out       BCRYPT_ALG_HANDLE   *phAlgorithm,
 __in        LPCWSTR pszAlgId,
 __in_opt    LPCWSTR pszImplementation,
 __in        ULONG   dwFlags);

extern NTSTATUS
(WINAPI
*fnBCryptGetProperty)(
				  __in                                        BCRYPT_HANDLE   hObject,
				  __in                                        LPCWSTR pszProperty,
				  __out_bcount_part_opt(cbOutput, *pcbResult) PUCHAR   pbOutput,
				  __in                                        ULONG   cbOutput,
				  __out                                       ULONG   *pcbResult,
				  __in                                        ULONG   dwFlags);

extern NTSTATUS (WINAPI*fnBCryptDestroyHash)(__inout BCRYPT_HASH_HANDLE hHash);
#endif // MSSIP32_H
