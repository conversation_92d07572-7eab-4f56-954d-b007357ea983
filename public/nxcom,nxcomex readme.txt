0. 建立windows工程，vs2019建立atcom工程注意事项：
     建立好工程后，除了常规nxcom注意事项外，还需要做一个设置：
    工程-属性-C/C++-语言-conformace mode 从Yes(/permissive-)模式修改为Deault模式, 不然编译会提示找不到'm_RefCount': 


   使用nxcomex库要定义 _STD_COM 宏


1. 建立工程时候，工程包含头文件commproxybase.h
2. comm类从CommProxyBase<T>模板派生
3. 默认所有com类都有IPlugin, IPluginRun, 所以只需要使用STD_QIX实现其他接口定义即可
4.为了实现统一，COMM类，不需要直接看到init, uninit,start,stop。而是看到
	virtual HRESULT OnAfterInit();
	virtual HRESULT OnBeforeUninit() ;
	virtual HRESULT OnAfterStart();
	virtual HRESULT OnBeforeStop();
5.rot被CommProxyBase<T>统一封装了，所以如果需要从ROT里面拿对象或者创建对象，使用如下方法：
   RotCreateInstance
   RotGetObject

6. 所有接口派生自ICommBase
7. COM智能指针使用SComPtr宏  SComPtr<IT>
8. 清空COM智能指针对象使用NullPtr(X)宏
9. 需要使用IID时候，要使用GetIID(x) ，例如：GetIID(IPlugin)
10. 定义IID使用：_DEF_IID 需要说明的是为了兼容nxcom,nxcomex，这里将这个做了扩容
    _DEF_IID(iface, uuid_string, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8)
11. 定义GUID使用_DEF_GUID 参数同nxcom
12. dllmain里面导出插件使用如下宏
     BEGIN_CLID_MAP()
          CLID_MAP_ENTRY(CID, CLASS, PROGID)
     END_CLID_MAP()

13. 需要注意这两个com类的def文件不同，需要在不同工程里面分别配置
14.使用nxcomex时候 需要单独多增加一个interface.cpp里面是相关iid，guid的实现。具体参考nxcomex的使用
