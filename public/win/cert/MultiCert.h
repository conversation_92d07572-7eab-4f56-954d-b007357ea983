#pragma once
#include <windows.h>
#include <wincrypt.h>
#include <string>
#include <list>

struct CertInfo {
	std::wstring issuer;
	std::wstring uer;
	DWORD        timeSpam;
};

class CMultiCert
{
public:
	CMultiCert();
	~CMultiCert();

	bool Load<PERSON>ert(LPCTSTR lpszPath, std::list<CertInfo>& allCert);
	bool LoadCert(HCRYPTMSG hMsg, CMSG_SIGNER_INFO* pSigner, std::list<CertInfo>& allCert);
private:
	bool CryptMsgGetParam_MsgSignerInfo(HCRYPTMSG hMsg, CMSG_SIGNER_INFO** ppSigner);
	bool RetrieveMoreCert(CMSG_SIGNER_INFO* lpFirstSignerInfo);
	bool GetCertDetailInfo(HCERTSTORE hStore, HCRYPTMSG hMsg, CMSG_SIGNER_INFO* lpSingerInfo);
	
	bool GetTimeSpamp(PCMSG_SIGNER_INFO pSignerInfo, SYSTEMTIME& st);

	PCRYPT_ATTRIBUTE FindAttr(CRYPT_ATTRIBUTES* lpAttrs, const char* lpszOID);
	bool DecodePkcsContentInfo(PCRYPT_ATTRIBUTE lpAttr);
	bool DecodeTimeStampFromRSA(PCRYPT_ATTRIBUTE lpAttr, SYSTEMTIME& st);
	BOOL Decode_CryptMsgOpenToDecode(PCRYPT_ATTR_BLOB lpBold, CMSG_SIGNER_INFO** lppFindSignerInfo);
	BOOL Decode_CryptDecodeObject(PCRYPT_ATTR_BLOB lpBold, CMSG_SIGNER_INFO** lppFindSignerInfo);

	std::wstring GetUserName(HCERTSTORE hStore, CMSG_SIGNER_INFO* lpSigner);
	std::wstring _CertNameToStr(PCERT_NAME_BLOB pName);
	std::wstring _CertIntegerToString(PCRYPT_INTEGER_BLOB pBlob);

private:
	std::list<CertInfo> m_allCert;
};

