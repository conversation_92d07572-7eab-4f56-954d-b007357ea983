#pragma once
#include <string>
#include <windows.h>
#include <vector>

class CWinProcInfo
{
public:
	CWinProcInfo();
	~CWinProcInfo();

	bool Init(DWORD pid);
	void Uninit();
	bool IsRunasAdmin();

	bool GetPEBInfo();
	const std::vector<std::string>& GetEnvironment();
	std::string GetImagePath();
	std::string GetCmdLine();
	std::string GetUser();

	static bool Is64BitProcess(DWORD pid);
	static std::string GetFileVersionInfo(const char* filepath, const char* tcValueType);
	static std::string GetProcCompanyInfo(const char* filepath) { return GetFileVersionInfo(filepath, "CompanyName"); };
	static std::string GetProcVersionInfo(const char* filepath) { return GetFileVersionInfo(filepath, "FileVersion"); };
	static std::string Utf16ToU8(const wchar_t* lpszStr);
	static std::wstring Utf8ToU16(const char* lpszStr);
private:
	bool GetEnvironment_S32_D32();
	bool GetEnvironment_S32_D64();
	bool GetEnvironment_S64();

	bool AssignEnvironment(BYTE* Environment);
	bool AssignImagePath(BYTE* ImagePath);
	bool AssignCmdLine(BYTE* CmdLine);

private:

	HANDLE m_ProcessHandle;
	std::vector<std::string> m_environment;
	std::string m_imagepath;
	std::string m_cmdline;
};

