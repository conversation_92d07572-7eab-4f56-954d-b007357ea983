#ifndef _IPLUGIN_H_
#define _IPLUGIN_H_

#include <dlcom/unknown.h>

uvStdComNameSpaceBegin

interface IPlugin : public IBase
{
	std_method(Init)(int Argc, basic_tchar* Argv[],void* Object) PURE;
	std_method(Uninit)() PURE;
};
	
// {33B172BE-68E7-4640-8624-11749B1E0B1A}
_DEFINE_IID(IID_IPlugin,
	0x33b172be, 0x68e7, 0x4640, 0x86, 0x24, 0x11, 0x74, 0x9b, 0x1e, 0xb, 0x1a);


interface IPluginRun : public IBase
{
	std_method(Start)(UINT uType) PURE;
	std_method(Stop)(UINT uExitCode) PURE;
};

// {B71AE479-F976-417A-9F12-46D9FAAD1E50}
_DEFINE_IID(IID_IPluginRun,
	0xb71ae479, 0xf976, 0x417a, 0x9f, 0x12, 0x46, 0xd9, 0xfa, 0xad, 0x1e, 0x50);


uvStdComNameSpaceEnd

#endif

