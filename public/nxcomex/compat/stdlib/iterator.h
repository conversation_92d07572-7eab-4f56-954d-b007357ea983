#ifndef _COMPAT_ITERATOR_H_
#define _COMPAT_ITERATOR_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef  __cplusplus
extern "C" 
{
#endif

typedef struct ITER ITER;

struct ITER {
	void *ptr;		
	void *data;	
	int   dlen;	
	const char *key;	
	int   klen;	
	int   i;
	int   size;		
};

#define	foreach_t(iter, container)  \
        for ((container)->iter_head(&(iter), (container));  \
             (iter).ptr;  \
             (container)->iter_next(&(iter), (container)))


#define	foreach_reverse_t(iter, container)  \
        for ((container)->iter_tail(&(iter), (container));  \
             (iter).ptr;  \
             (container)->iter_prev(&(iter), (container)))

#define	ITER_INFO(iter, container)  \
	(container)->iter_info(&(iter), (container))

#define	foreach_reverse		foreach_reverse_t
#define	foreach				foreach_t


#ifdef __cplusplus
}
#endif

#endif

