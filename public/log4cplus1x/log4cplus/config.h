/* include/log4cplus/config.h.  Generated from config.h.in by configure.  */
/* include/log4cplus/config.h.in.  Generated from configure.ac by autoheader.  */

#ifndef LOG4CPLUS_CONFIG_H

#define LOG4CPLUS_CONFIG_H

/* Defined if the compiler supports C99 style variadic macros with
   __VA_ARGS__. */
/* #undef HAS_C99_VARIADIC_MACROS */

/* Defined if the compiler supports GNU style variadic macros. */
/* #undef HAS_GNU_VARIADIC_MACROS */

/* Defined if the compiler provides atomic_dec_uint_nv(). */
/* #undef HAVE_ATOMIC_DEC_UINT_NV */

/* Defined if the compiler provides atomic_inc_uint(). */
/* #undef HAVE_ATOMIC_INC_UINT */

/* Define to 1 if you have the `clock_gettime' function. */
#define HAVE_CLOCK_GETTIME 1

/* Define to 1 if you have the `clock_nanosleep' function. */
#define HAVE_CLOCK_NANOSLEEP 1

/* Defined if the compiler provides C++11 <atomic> header and increment,
   decrement operations. */
#define HAVE_CXX11_ATOMICS 1

/* Define to 1 if you have the <dlfcn.h> header file. */
#define HAVE_DLFCN_H 1

/* Define to 1 if you have the `fcntl' function. */
#define HAVE_FCNTL 1

/* Define to 1 if you have the `flock' function. */
#define HAVE_FLOCK 1

/* Define to 1 if you have the `ftime' function. */
#define HAVE_FTIME 1

/* Define to 1 if the system has the `constructor' function attribute */
#define HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR 1

/* Define to 1 if the system has the `constructor_priority' function attribute
   */
#define HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR_PRIORITY 1

/* */
#define HAVE_GETADDRINFO 1

/* */
#define HAVE_GETHOSTBYNAME_R 1

/* Define to 1 if you have the `getpid' function. */
#define HAVE_GETPID 1

/* Define to 1 if you have the `gettimeofday' function. */
#define HAVE_GETTIMEOFDAY 1

/* Define to 1 if you have the `gmtime_r' function. */
#define HAVE_GMTIME_R 1

/* Define to 1 if you have the `htonl' function. */
#define HAVE_HTONL 1

/* Define to 1 if you have the `htons' function. */
#define HAVE_HTONS 1

/* Define to 1 if you have the `iconv' function. */
/* #undef HAVE_ICONV */

/* Define to 1 if you have the `iconv_close' function. */
/* #undef HAVE_ICONV_CLOSE */

/* Define to 1 if you have the `iconv_open' function. */
/* #undef HAVE_ICONV_OPEN */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `advapi32' library (-ladvapi32). */
/* #undef HAVE_LIBADVAPI32 */

/* Define to 1 if you have the `libiconv' function. */
/* #undef HAVE_LIBICONV */

/* Define to 1 if you have the `libiconv_close' function. */
/* #undef HAVE_LIBICONV_CLOSE */

/* Define to 1 if you have the `libiconv_open' function. */
/* #undef HAVE_LIBICONV_OPEN */

/* Define to 1 if you have the `kernel32' library (-lkernel32). */
/* #undef HAVE_LIBKERNEL32 */

/* Define to 1 if you have the `oleaut32' library (-loleaut32). */
/* #undef HAVE_LIBOLEAUT32 */

/* Define to 1 if you have the `ws2_32' library (-lws2_32). */
/* #undef HAVE_LIBWS2_32 */

/* Define to 1 if you have the `localtime_r' function. */
#define HAVE_LOCALTIME_R 1

/* Define to 1 if you have the `lockf' function. */
#define HAVE_LOCKF 1

/* Define to 1 if you have the `lstat' function. */
#define HAVE_LSTAT 1

/* Define to 1 if you have the `mbstowcs' function. */
#define HAVE_MBSTOWCS 1

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the `nanosleep' function. */
#define HAVE_NANOSLEEP 1

/* Define to 1 if you have the `ntohl' function. */
#define HAVE_NTOHL 1

/* Define to 1 if you have the `ntohs' function. */
#define HAVE_NTOHS 1

/* Define to 1 if you have the `OutputDebugStringW' function. */
/* #undef HAVE_OUTPUTDEBUGSTRINGW */

/* Define to 1 if you have the `pipe' function. */
#define HAVE_PIPE 1

/* Define to 1 if you have the `pipe2' function. */
#define HAVE_PIPE2 1

/* Define to 1 if you have the `poll' function. */
#define HAVE_POLL 1

/* Define if you have POSIX threads libraries and header files. */
#define HAVE_PTHREAD 1

/* Have PTHREAD_PRIO_INHERIT. */
#define HAVE_PTHREAD_PRIO_INHERIT 1

/* If available, contains the Python version number currently in use. */
/* #undef HAVE_PYTHON */

/* Define to 1 if you have the `shutdown' function. */
#define HAVE_SHUTDOWN 1

/* Define to 1 if you have the `stat' function. */
#define HAVE_STAT 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Defined if the compiler understands __thread or __declspec(thread)
   construct. */
#define HAVE_TLS_SUPPORT 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Define to 1 if the system has the `init_priority' variable attribute */
#define HAVE_VAR_ATTRIBUTE_INIT_PRIORITY 1

/* Define to 1 if you have the `vfprintf_s' function. */
/* #undef HAVE_VFPRINTF_S */

/* Define to 1 if you have the `vfwprintf_s' function. */
/* #undef HAVE_VFWPRINTF_S */

/* Define to 1 if you have the `vsnprintf' function. */
#define HAVE_VSNPRINTF 1

/* Define to 1 if you have the `vsnwprintf' function. */
/* #undef HAVE_VSNWPRINTF */

/* Define to 1 if you have the `vsprintf_s' function. */
/* #undef HAVE_VSPRINTF_S */

/* Define to 1 if you have the `vswprintf_s' function. */
/* #undef HAVE_VSWPRINTF_S */

/* Define to 1 if you have the `wcstombs' function. */
#define HAVE_WCSTOMBS 1

/* Define to 1 if you have the `_vsnprintf' function. */
/* #undef HAVE__VSNPRINTF */

/* Define to 1 if you have the `_vsnprintf_s' function. */
/* #undef HAVE__VSNPRINTF_S */

/* Define to 1 if you have the `_vsnwprintf' function. */
/* #undef HAVE__VSNWPRINTF */

/* Define to 1 if you have the `_vsnwprintf_s' function. */
/* #undef HAVE__VSNWPRINTF_S */

/* Defined if the compiler provides __atomic_add_fetch(). */
#define HAVE___ATOMIC_ADD_FETCH 1

/* Defined if the compiler provides __atomic_sub_fetch(). */
#define HAVE___ATOMIC_SUB_FETCH 1

/* Defined if the compiler supports __FUNCTION__ macro. */
/* #undef HAVE___FUNCTION___MACRO */

/* Defined if the compiler supports __func__ symbol. */
/* #undef HAVE___FUNC___SYMBOL */

/* Defined if the compiler supports __PRETTY_FUNCTION__ macro. */
/* #undef HAVE___PRETTY_FUNCTION___MACRO */

/* Defined if the compiler provides __sync_add_and_fetch(). */
#define HAVE___SYNC_ADD_AND_FETCH 1

/* Defined if the compiler provides __sync_sub_and_fetch(). */
#define HAVE___SYNC_SUB_AND_FETCH 1

/* Defined for --enable-debugging builds. */
/* #undef LOG4CPLUS_DEBUGGING */

/* Defined if the compiler understands __declspec(dllimport) or
   __attribute__((visibility("default"))) or __global construct. */
#define LOG4CPLUS_DECLSPEC_EXPORT __attribute__ ((visibility("default")))

/* Defined if the compiler understands __declspec(dllimport) or
   __attribute__((visibility("default"))) or __global construct. */
#define LOG4CPLUS_DECLSPEC_IMPORT __attribute__ ((visibility("default")))

/* Defined if the compiler understands __attribute__((visibility("hidden")))
   or __hidden construct. */
#define LOG4CPLUS_DECLSPEC_PRIVATE __attribute__ ((visibility("hidden")))

/* */
#define LOG4CPLUS_HAVE_ARPA_INET_H 1

/* */
/* #undef LOG4CPLUS_HAVE_ATOMIC_DEC_UINT_NV */

/* */
/* #undef LOG4CPLUS_HAVE_ATOMIC_H */

/* */
/* #undef LOG4CPLUS_HAVE_ATOMIC_INC_UINT */

/* */
#define LOG4CPLUS_HAVE_C99_VARIADIC_MACROS 1

/* */
#define LOG4CPLUS_HAVE_CLOCK_GETTIME 1

/* */
#define LOG4CPLUS_HAVE_CLOCK_NANOSLEEP 1

/* */
#define LOG4CPLUS_HAVE_CXX11_ATOMICS 1

/* */
#define LOG4CPLUS_HAVE_ENAMETOOLONG 1

/* */
#define LOG4CPLUS_HAVE_ERRNO_H 1

/* */
#define LOG4CPLUS_HAVE_FCNTL 1

/* */
#define LOG4CPLUS_HAVE_FCNTL_H 1

/* */
#define LOG4CPLUS_HAVE_FLOCK 1

/* */
#define LOG4CPLUS_HAVE_FTIME 1

/* */
#define LOG4CPLUS_HAVE_FUNCTION_MACRO 1

/* */
#define LOG4CPLUS_HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR 1

/* */
#define LOG4CPLUS_HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR_PRIORITY 1

/* */
#define LOG4CPLUS_HAVE_FUNC_SYMBOL 1

/* */
#define LOG4CPLUS_HAVE_GETADDRINFO 1

/* */
#define LOG4CPLUS_HAVE_GETHOSTBYNAME_R 1

/* */
#define LOG4CPLUS_HAVE_GETPID 1

/* */
#define LOG4CPLUS_HAVE_GETTID 1

/* */
#define LOG4CPLUS_HAVE_GETTIMEOFDAY 1

/* */
#define LOG4CPLUS_HAVE_GMTIME_R 1

/* */
#define LOG4CPLUS_HAVE_GNU_VARIADIC_MACROS 1

/* */
#define LOG4CPLUS_HAVE_HTONL 1

/* */
#define LOG4CPLUS_HAVE_HTONS 1

/* */
/* #undef LOG4CPLUS_HAVE_ICONV */

/* */
/* #undef LOG4CPLUS_HAVE_ICONV_CLOSE */

/* */
/* #undef LOG4CPLUS_HAVE_ICONV_H */

/* */
/* #undef LOG4CPLUS_HAVE_ICONV_OPEN */

/* */
#define LOG4CPLUS_HAVE_LIMITS_H 1

/* */
#define LOG4CPLUS_HAVE_LOCALTIME_R 1

/* */
#define LOG4CPLUS_HAVE_LOCKF 1

/* */
#define LOG4CPLUS_HAVE_LSTAT 1

/* */
#define LOG4CPLUS_HAVE_MBSTOWCS 1

/* */
#define LOG4CPLUS_HAVE_NANOSLEEP 1

/* */
#define LOG4CPLUS_HAVE_NETDB_H 1

/* */
#define LOG4CPLUS_HAVE_NETINET_IN_H 1

/* */
#define LOG4CPLUS_HAVE_NETINET_TCP_H 1

/* */
#define LOG4CPLUS_HAVE_NTOHL 1

/* */
#define LOG4CPLUS_HAVE_NTOHS 1

/* */
/* #undef LOG4CPLUS_HAVE_OUTPUTDEBUGSTRING */

/* */
#define LOG4CPLUS_HAVE_PIPE 1

/* */
#define LOG4CPLUS_HAVE_PIPE2 1

/* */
#define LOG4CPLUS_HAVE_POLL 1

/* */
#define LOG4CPLUS_HAVE_POLL_H 1

/* */
#define LOG4CPLUS_HAVE_PRETTY_FUNCTION_MACRO 1

/* */
#define LOG4CPLUS_HAVE_SHUTDOWN 1

/* */
#define LOG4CPLUS_HAVE_STAT 1

/* */
#define LOG4CPLUS_HAVE_STDARG_H 1

/* */
#define LOG4CPLUS_HAVE_STDIO_H 1

/* */
#define LOG4CPLUS_HAVE_STDLIB_H 1

/* */
#define LOG4CPLUS_HAVE_SYSLOG_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_FILE_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_SOCKET_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_STAT_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_SYSCALL_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_TIMEB_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_TIME_H 1

/* */
#define LOG4CPLUS_HAVE_SYS_TYPES_H 1

/* */
#define LOG4CPLUS_HAVE_TIME_H 1

/* */
#define LOG4CPLUS_HAVE_TLS_SUPPORT 1

/* */
#define LOG4CPLUS_HAVE_UNISTD_H 1

/* */
#define LOG4CPLUS_HAVE_VAR_ATTRIBUTE_INIT_PRIORITY 1

/* */
/* #undef LOG4CPLUS_HAVE_VFPRINTF_S */

/* */
/* #undef LOG4CPLUS_HAVE_VFWPRINTF_S */

/* */
#define LOG4CPLUS_HAVE_VSNPRINTF 1

/* */
/* #undef LOG4CPLUS_HAVE_VSNWPRINTF */

/* */
/* #undef LOG4CPLUS_HAVE_VSPRINTF_S */

/* */
/* #undef LOG4CPLUS_HAVE_VSWPRINTF_S */

/* */
#define LOG4CPLUS_HAVE_WCHAR_H 1

/* */
#define LOG4CPLUS_HAVE_WCSTOMBS 1

/* */
/* #undef LOG4CPLUS_HAVE__VSNPRINTF */

/* */
/* #undef LOG4CPLUS_HAVE__VSNPRINTF_S */

/* */
/* #undef LOG4CPLUS_HAVE__VSNWPRINTF */

/* */
/* #undef LOG4CPLUS_HAVE__VSNWPRINTF_S */

/* */
#define LOG4CPLUS_HAVE___ATOMIC_ADD_FETCH 1

/* */
#define LOG4CPLUS_HAVE___ATOMIC_SUB_FETCH 1

/* */
#define LOG4CPLUS_HAVE___SYNC_ADD_AND_FETCH 1

/* */
#define LOG4CPLUS_HAVE___SYNC_SUB_AND_FETCH 1

/* Define if this is a single-threaded library. */
/* #undef LOG4CPLUS_SINGLE_THREADED */

/* */
#define LOG4CPLUS_THREAD_LOCAL_VAR thread_local

/* */
/* #undef LOG4CPLUS_USE_PTHREADS */

/* Define so that log4cplus will use C++11 threads and synchronization
   primitives. */
/* #undef LOG4CPLUS_WITH_CXX11_THREADS */

/* Define when iconv() is available. */
/* #undef LOG4CPLUS_WITH_ICONV */

/* Define for C99 compilers/standard libraries that support more than just the
   "C" locale. */
/* #undef LOG4CPLUS_WORKING_C_LOCALE */

/* Define for compilers/standard libraries that support more than just the "C"
   locale. */
/* #undef LOG4CPLUS_WORKING_LOCALE */

/* Define to the sub-directory in which libtool stores uninstalled libraries.
   */
#define LT_OBJDIR ".libs/"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT ""

/* Define to the full name of this package. */
#define PACKAGE_NAME "log4cplus"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "log4cplus 1.2.2"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "log4cplus"

/* Define to the home page for this package. */
#define PACKAGE_URL ""

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.2.2"

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
/* #undef PTHREAD_CREATE_JOINABLE */

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Defined to the actual TLS support construct. */
#define TLS_SUPPORT_CONSTRUCT thread_local

/* Substitute for socklen_t */
/* #undef socklen_t */

#endif // LOG4CPLUS_CONFIG_H
