#ifndef _COMPAT_DEFINE_H_
#define _COMPAT_DEFINE_H_

#include <util/util.h>
#include <compat/def.h>


#ifdef __cplusplus
extern "C" 
{
#endif

#define __ascii_iswalpha(c)  ( ('A' <= (c) && (c) <= 'Z') || ( 'a' <= (c) && (c) <= 'z'))
#define __ascii_iswdigit(c)  ( '0' <= (c) && (c) <= '9')
#define __ascii_tolower(c)   ( (((c) >= 'A') && ((c) <= 'Z')) ? ((c) - 'A' + 'a') : (c) )
#define __ascii_toupper(c)   ( (((c) >= 'a') && ((c) <= 'z')) ? ((c) - 'a' + 'A') : (c) )

#ifdef __cplusplus
}
#endif

#endif
