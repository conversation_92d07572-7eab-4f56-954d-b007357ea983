#ifndef _UTIL_OSDEF_H_
#define _UTIL_OSDEF_H_


#include <winsock2.h>
#include <windows.h>
#include <process.h>
#include <shlwapi.h>
#include <tchar.h>

#pragma warning(disable:4996)
#define	FMT_I64D	"%I64d"
#define	FMT_I64U	"%I64u"

#ifndef _SSIZE_T_
#define _SSIZE_T_

typedef SSIZE_T ssize_t;

#endif // !_SSIZE_T_

typedef signed char        int8_t;
typedef short              int16_t;
typedef int                int32_t;
typedef long long          int64_t;
typedef unsigned char      uint8_t;
typedef unsigned short     uint16_t;
typedef unsigned int       uint32_t;
typedef unsigned long long uint64_t;



#endif
